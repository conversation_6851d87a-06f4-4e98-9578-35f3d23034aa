/**
 * WP Favorites Admin Styles
 * Professional, lightweight design for admin interface
 */

/* ==========================================================================
   Admin Layout & Structure
   ========================================================================== */

.wp-favorites-admin {
    max-width: 1200px;
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.wp-favorites-admin .wrap {
    margin: 20px 0 0 0;
}

.wp-favorites-admin h1 {
    font-size: 23px;
    font-weight: 400;
    margin: 0 0 20px 0;
    color: #23282d;
    display: flex;
    align-items: center;
    gap: 10px;
}

.wp-favorites-admin h1::before {
    content: "❤️";
    font-size: 20px;
}

/* ==========================================================================
   Navigation Tabs
   ========================================================================== */

.wp-favorites-admin .nav-tab-wrapper {
    border-bottom: 1px solid #ccd0d4;
    margin: 0 0 20px 0;
    padding: 0;
}

.wp-favorites-admin .nav-tab {
    background: #f1f1f1;
    border: 1px solid #ccd0d4;
    border-bottom: none;
    color: #555;
    text-decoration: none;
    padding: 12px 16px;
    margin: 0 4px 0 0;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    position: relative;
    top: 1px;
}

.wp-favorites-admin .nav-tab:hover {
    background: #fff;
    color: #23282d;
}

.wp-favorites-admin .nav-tab-active {
    background: #fff;
    border-bottom: 1px solid #fff;
    color: #23282d;
    font-weight: 600;
}

/* ==========================================================================
   Form Styles
   ========================================================================== */

.wp-favorites-admin .form-table {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    margin: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

.wp-favorites-admin .form-table th {
    background: #f9f9f9;
    border-bottom: 1px solid #eee;
    padding: 20px;
    font-weight: 600;
    color: #23282d;
    width: 200px;
    vertical-align: top;
}

.wp-favorites-admin .form-table td {
    padding: 20px;
    border-bottom: 1px solid #eee;
    vertical-align: top;
}

.wp-favorites-admin .form-table tr:last-child th,
.wp-favorites-admin .form-table tr:last-child td {
    border-bottom: none;
}

.wp-favorites-admin .form-table input[type="text"],
.wp-favorites-admin .form-table input[type="number"],
.wp-favorites-admin .form-table select,
.wp-favorites-admin .form-table textarea {
    width: 100%;
    max-width: 400px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.wp-favorites-admin .form-table input[type="text"]:focus,
.wp-favorites-admin .form-table input[type="number"]:focus,
.wp-favorites-admin .form-table select:focus,
.wp-favorites-admin .form-table textarea:focus {
    border-color: #007cba;
    outline: none;
    box-shadow: 0 0 0 1px #007cba;
}

.wp-favorites-admin .form-table .description {
    color: #666;
    font-size: 13px;
    margin: 8px 0 0 0;
    line-height: 1.4;
}

/* Checkbox styles */
.wp-favorites-admin .form-table input[type="checkbox"] {
    margin: 0 8px 0 0;
    transform: scale(1.1);
}

.wp-favorites-admin .form-table label {
    display: flex;
    align-items: center;
    font-weight: 500;
    color: #23282d;
}

/* ==========================================================================
   Translation Management
   ========================================================================== */

.translation-sections {
    display: grid;
    gap: 30px;
}

.translation-category {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

.translation-category h3 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    margin: 0;
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #ccd0d4;
}

.translation-category .form-table {
    border: none;
    box-shadow: none;
    margin: 0;
}

.translation-category .form-table th {
    background: #f8f9fa;
    width: 250px;
}

.translation-category .form-table th label {
    font-weight: 600;
    color: #23282d;
    margin-bottom: 4px;
}

.translation-category .form-table th .description {
    font-weight: normal;
    color: #666;
    font-size: 12px;
}

.translation-category .form-table th .description code {
    background: #f1f1f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    color: #d63384;
}

/* ==========================================================================
   Performance Dashboard
   ========================================================================== */

.performance-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.performance-stat {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
    transition: transform 0.2s ease;
}

.performance-stat:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.performance-stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #007cba;
    margin: 0 0 8px 0;
}

.performance-stat-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.performance-stat.success .performance-stat-value {
    color: #27ae60;
}

.performance-stat.warning .performance-stat-value {
    color: #f39c12;
}

.performance-stat.error .performance-stat-value {
    color: #e74c3c;
}

/* ==========================================================================
   Buttons
   ========================================================================== */

.wp-favorites-admin .button {
    background: #007cba;
    border-color: #007cba;
    color: #fff;
    text-decoration: none;
    display: inline-block;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.wp-favorites-admin .button:hover {
    background: #005a87;
    border-color: #005a87;
    color: #fff;
    transform: translateY(-1px);
}

.wp-favorites-admin .button:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

.wp-favorites-admin .button-secondary {
    background: #f1f1f1;
    border-color: #ccd0d4;
    color: #23282d;
}

.wp-favorites-admin .button-secondary:hover {
    background: #fff;
    border-color: #999;
    color: #23282d;
}

.wp-favorites-admin .button-primary {
    background: #007cba;
    border-color: #007cba;
    color: #fff;
    font-weight: 600;
    padding: 10px 20px;
}

.wp-favorites-admin .button-primary:hover {
    background: #005a87;
    border-color: #005a87;
}

/* ==========================================================================
   Icon Upload
   ========================================================================== */

.icon-upload-area {
    border: 2px dashed #ccd0d4;
    border-radius: 6px;
    padding: 40px 20px;
    text-align: center;
    background: #f9f9f9;
    transition: all 0.2s ease;
    cursor: pointer;
}

.icon-upload-area:hover {
    border-color: #007cba;
    background: #f0f8ff;
}

.icon-upload-area.dragover {
    border-color: #007cba;
    background: #e6f3ff;
}

.icon-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 10px;
    margin: 20px 0;
}

.icon-preview-item {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    text-align: center;
    position: relative;
    transition: all 0.2s ease;
}

.icon-preview-item:hover {
    border-color: #007cba;
    transform: scale(1.05);
}

.icon-preview-item.active {
    border-color: #007cba;
    background: #f0f8ff;
}

.icon-preview-item img {
    max-width: 100%;
    height: 40px;
    object-fit: contain;
}

.icon-preview-item .remove-icon {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #e74c3c;
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    cursor: pointer;
    display: none;
}

.icon-preview-item:hover .remove-icon {
    display: block;
}

/* ==========================================================================
   Notices & Alerts
   ========================================================================== */

.wp-favorites-notice {
    background: #fff;
    border-left: 4px solid #007cba;
    padding: 12px 16px;
    margin: 20px 0;
    border-radius: 0 4px 4px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

.wp-favorites-notice.success {
    border-left-color: #27ae60;
}

.wp-favorites-notice.warning {
    border-left-color: #f39c12;
}

.wp-favorites-notice.error {
    border-left-color: #e74c3c;
}

.wp-favorites-notice p {
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.wp-favorites-loading {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 14px;
}

.wp-favorites-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: wp-favorites-spin 1s linear infinite;
}

@keyframes wp-favorites-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .wp-favorites-admin .form-table th,
    .wp-favorites-admin .form-table td {
        display: block;
        width: 100%;
        padding: 15px;
    }
    
    .wp-favorites-admin .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }
    
    .performance-stats {
        grid-template-columns: 1fr;
    }
    
    .translation-category .form-table th {
        width: 100%;
    }
}

/* ==========================================================================
   Accessibility
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
    .wp-favorites-admin *,
    .wp-favorites-admin *::before,
    .wp-favorites-admin *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for keyboard navigation */
.wp-favorites-admin *:focus-visible {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .wp-favorites-admin .form-table,
    .wp-favorites-admin .translation-category,
    .wp-favorites-admin .performance-stat {
        border-width: 2px;
    }
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.wp-favorites-admin .text-center {
    text-align: center;
}

.wp-favorites-admin .text-right {
    text-align: right;
}

.wp-favorites-admin .mb-0 {
    margin-bottom: 0;
}

.wp-favorites-admin .mt-20 {
    margin-top: 20px;
}

.wp-favorites-admin .hidden {
    display: none;
}

.wp-favorites-admin .flex {
    display: flex;
}

.wp-favorites-admin .items-center {
    align-items: center;
}

.wp-favorites-admin .gap-10 {
    gap: 10px;
}
