<?php
/**
 * Plugin Name: WP Favorites
 * Plugin URI: https://github.com/pixelhunter1/wp-favorites
 * Description: High-performance favorites system for WooCommerce with Breakdance integration and multilingual support.
 * Version: 1.0.0
 * Author: Pixel<PERSON>unter
 * Author URI: https://pixelhunter.dev
 * Text Domain: wp-favorites
 * Domain Path: /languages
 * Requires at least: 6.0
 * Tested up to: 6.4
 * Requires PHP: 8.0
 * WC requires at least: 7.0
 * WC tested up to: 8.5
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WP_FAVORITES_VERSION', '1.0.0');
define('WP_FAVORITES_PLUGIN_FILE', __FILE__);
define('WP_FAVORITES_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WP_FAVORITES_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WP_FAVORITES_TEXT_DOMAIN', 'wp-favorites');

/**
 * Main WP Favorites Plugin Class
 */
class WP_Favorites_Plugin {
    
    /**
     * Single instance of the plugin
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Plugin activation/deactivation
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        // Initialize plugin
        add_action('plugins_loaded', array($this, 'init'));
        
        // Load text domain
        add_action('init', array($this, 'load_textdomain'));
        
        // Check dependencies
        add_action('admin_init', array($this, 'check_dependencies'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Always load admin for configuration (regardless of WooCommerce status)
        require_once WP_FAVORITES_PLUGIN_DIR . 'includes/class-favorites-admin.php';
        if (is_admin() && class_exists('WP_Favorites_Admin')) {
            new WP_Favorites_Admin();
        }

        // Check if WooCommerce is active for full functionality
        if (!$this->is_woocommerce_active()) {
            return;
        }

        // Load includes
        $this->load_includes();

        // Initialize core (but skip admin initialization there)
        if (class_exists('WP_Favorites_Core')) {
            WP_Favorites_Core::get_instance();
        }
    }
    
    /**
     * Load plugin text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            WP_FAVORITES_TEXT_DOMAIN,
            false,
            dirname(plugin_basename(__FILE__)) . '/languages/'
        );
    }
    
    /**
     * Load required files
     */
    private function load_includes() {
        // Core classes
        require_once WP_FAVORITES_PLUGIN_DIR . 'includes/class-favorites-core.php';
        require_once WP_FAVORITES_PLUGIN_DIR . 'includes/class-favorites-admin.php';
        require_once WP_FAVORITES_PLUGIN_DIR . 'includes/class-favorites-frontend.php';
        require_once WP_FAVORITES_PLUGIN_DIR . 'includes/class-favorites-ajax.php';
        require_once WP_FAVORITES_PLUGIN_DIR . 'includes/class-favorites-cache.php';
        require_once WP_FAVORITES_PLUGIN_DIR . 'includes/class-favorites-performance.php';
        
        // Breakdance integration (if available)
        if ($this->is_breakdance_active()) {
            $breakdance_file = WP_FAVORITES_PLUGIN_DIR . 'includes/class-favorites-breakdance.php';
            if (file_exists($breakdance_file)) {
                require_once $breakdance_file;
            }
        }
    }
    
    /**
     * Check if WooCommerce is active
     */
    private function is_woocommerce_active() {
        return class_exists('WooCommerce');
    }
    
    /**
     * Check if Breakdance is active
     */
    private function is_breakdance_active() {
        return defined('BREAKDANCE_PLUGIN_URL');
    }
    
    /**
     * Check plugin dependencies
     */
    public function check_dependencies() {
        if (!$this->is_woocommerce_active()) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            deactivate_plugins(plugin_basename(__FILE__));
        }
    }
    
    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p>
                <?php 
                echo sprintf(
                    /* translators: %s: Plugin name */
                    esc_html__('%s requires WooCommerce to be installed and active.', 'wp-favorites'),
                    '<strong>WP Favorites</strong>'
                );
                ?>
            </p>
        </div>
        <?php
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        $this->create_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Set activation flag
        update_option('wp_favorites_activated', true);
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled events
        wp_clear_scheduled_hook('wp_favorites_cleanup');
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Create database tables
     */
    private function create_tables() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'wp_favorites';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            product_id bigint(20) NOT NULL,
            date_added datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_product (user_id, product_id),
            KEY user_id (user_id),
            KEY product_id (product_id),
            KEY date_added (date_added)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $defaults = array(
            'favorites_page_id' => 0,
            'icon_position' => 'top-right',
            'icon_size' => 'medium',
            'custom_icon' => '',
            'enable_cache' => true,
            'cache_duration' => 3600,
            'enable_notifications' => true,
            'notification_duration' => 3000,
            'display_locations' => array(
                'shop_loop' => true,
                'single_product' => true,
                'related_products' => true,
                'cross_sells' => true,
                'up_sells' => true,
                'widgets' => true,
                'search_results' => true,
                'category_pages' => true
            ),
        );

        foreach ($defaults as $key => $value) {
            if (get_option('wp_favorites_' . $key) === false) {
                update_option('wp_favorites_' . $key, $value);
            }
        }
    }
}

// Initialize the plugin
function wp_favorites_init() {
    return WP_Favorites_Plugin::get_instance();
}



// Start the plugin
wp_favorites_init();

// Debug: Show admin instance count
add_action('admin_notices', function() {
    if (current_user_can('manage_options') && class_exists('WP_Favorites_Admin')) {
        $count = WP_Favorites_Admin::get_instance_count();
        if ($count > 1) {
            echo '<div class="notice notice-warning"><p>WP Favorites Admin instantiated ' . $count . ' times (should be 1)</p></div>';
        } else {
            echo '<div class="notice notice-success"><p>WP Favorites Admin correctly instantiated once</p></div>';
        }
    }
});

// Declare HPOS compatibility
add_action('before_woocommerce_init', function() {
    if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
        \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility(
            'custom_order_tables',
            __FILE__,
            true
        );
    }
});
