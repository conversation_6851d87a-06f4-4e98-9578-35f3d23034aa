<?php
/**
 * WP Favorites AJAX Class
 * 
 * Handles AJAX requests for favorites functionality
 */

if (!defined('ABSPATH')) {
    exit;
}

class WP_Favorites_Ajax {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // AJAX actions for logged in users
        add_action('wp_ajax_wp_favorites_toggle', array($this, 'toggle_favorite'));
        add_action('wp_ajax_wp_favorites_remove', array($this, 'remove_favorite'));
        add_action('wp_ajax_wp_favorites_get_list', array($this, 'get_favorites_list'));
        add_action('wp_ajax_wp_favorites_clear_all', array($this, 'clear_all_favorites'));
        
        // AJAX actions for non-logged in users (if needed)
        add_action('wp_ajax_nopriv_wp_favorites_toggle', array($this, 'require_login'));
        add_action('wp_ajax_nopriv_wp_favorites_remove', array($this, 'require_login'));
        add_action('wp_ajax_nopriv_wp_favorites_get_list', array($this, 'require_login'));
        add_action('wp_ajax_nopriv_wp_favorites_clear_all', array($this, 'require_login'));
    }
    
    /**
     * Toggle favorite status
     */
    public function toggle_favorite() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'wp_favorites_nonce')) {
            wp_send_json_error(array(
                'message' => __('Security check failed', 'wp-favorites')
            ));
        }
        
        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error(array(
                'message' => __('Please log in to use favorites', 'wp-favorites'),
                'code' => 'login_required'
            ));
        }
        
        $product_id = intval($_POST['product_id'] ?? 0);
        $user_id = get_current_user_id();
        
        // Validate product ID
        if (!$product_id || !wc_get_product($product_id)) {
            wp_send_json_error(array(
                'message' => __('Product not found', 'wp-favorites'),
                'code' => 'product_not_found'
            ));
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'wp_favorites';
        
        // Check if already favorite
        $exists = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE user_id = %d AND product_id = %d",
            $user_id,
            $product_id
        ));
        
        if ($exists) {
            // Remove from favorites
            $result = $wpdb->delete(
                $table_name,
                array(
                    'user_id' => $user_id,
                    'product_id' => $product_id
                ),
                array('%d', '%d')
            );
            
            if ($result !== false) {
                // Clear cache
                $this->clear_user_cache($user_id);
                
                wp_send_json_success(array(
                    'action' => 'removed',
                    'message' => __('Product removed from favorites', 'wp-favorites'),
                    'is_favorite' => false,
                    'product_id' => $product_id
                ));
            } else {
                wp_send_json_error(array(
                    'message' => __('Failed to remove product from favorites', 'wp-favorites'),
                    'code' => 'remove_failed'
                ));
            }
        } else {
            // Add to favorites
            $result = $wpdb->insert(
                $table_name,
                array(
                    'user_id' => $user_id,
                    'product_id' => $product_id,
                    'date_added' => current_time('mysql')
                ),
                array('%d', '%d', '%s')
            );
            
            if ($result !== false) {
                // Clear cache
                $this->clear_user_cache($user_id);
                
                wp_send_json_success(array(
                    'action' => 'added',
                    'message' => __('Product added to favorites', 'wp-favorites'),
                    'is_favorite' => true,
                    'product_id' => $product_id
                ));
            } else {
                wp_send_json_error(array(
                    'message' => __('Failed to add product to favorites', 'wp-favorites'),
                    'code' => 'add_failed'
                ));
            }
        }
    }
    
    /**
     * Remove specific favorite
     */
    public function remove_favorite() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'wp_favorites_nonce')) {
            wp_send_json_error(array(
                'message' => __('Security check failed', 'wp-favorites')
            ));
        }
        
        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error(array(
                'message' => __('Please log in to use favorites', 'wp-favorites'),
                'code' => 'login_required'
            ));
        }
        
        $product_id = intval($_POST['product_id'] ?? 0);
        $user_id = get_current_user_id();
        
        if (!$product_id) {
            wp_send_json_error(array(
                'message' => __('Invalid product ID', 'wp-favorites'),
                'code' => 'invalid_product'
            ));
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'wp_favorites';
        
        $result = $wpdb->delete(
            $table_name,
            array(
                'user_id' => $user_id,
                'product_id' => $product_id
            ),
            array('%d', '%d')
        );
        
        if ($result !== false) {
            // Clear cache
            $this->clear_user_cache($user_id);
            
            wp_send_json_success(array(
                'message' => __('Product removed from favorites', 'wp-favorites'),
                'product_id' => $product_id
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to remove product from favorites', 'wp-favorites'),
                'code' => 'remove_failed'
            ));
        }
    }
    
    /**
     * Get user's favorites list
     */
    public function get_favorites_list() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'wp_favorites_nonce')) {
            wp_send_json_error(array(
                'message' => __('Security check failed', 'wp-favorites')
            ));
        }
        
        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error(array(
                'message' => __('Please log in to view favorites', 'wp-favorites'),
                'code' => 'login_required'
            ));
        }
        
        $user_id = get_current_user_id();
        $page = intval($_POST['page'] ?? 1);
        $per_page = intval($_POST['per_page'] ?? 12);
        $offset = ($page - 1) * $per_page;
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'wp_favorites';
        
        // Get total count
        $total = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE user_id = %d",
            $user_id
        ));
        
        // Get favorites with pagination
        $favorites = $wpdb->get_results($wpdb->prepare(
            "SELECT product_id, date_added FROM $table_name 
             WHERE user_id = %d 
             ORDER BY date_added DESC 
             LIMIT %d OFFSET %d",
            $user_id,
            $per_page,
            $offset
        ));
        
        $products = array();
        foreach ($favorites as $favorite) {
            $product = wc_get_product($favorite->product_id);
            if ($product) {
                $products[] = array(
                    'id' => $product->get_id(),
                    'name' => $product->get_name(),
                    'price' => $product->get_price_html(),
                    'image' => wp_get_attachment_image_url($product->get_image_id(), 'medium'),
                    'url' => $product->get_permalink(),
                    'date_added' => $favorite->date_added
                );
            }
        }
        
        wp_send_json_success(array(
            'products' => $products,
            'total' => intval($total),
            'page' => $page,
            'per_page' => $per_page,
            'total_pages' => ceil($total / $per_page),
            'message' => $total > 0 ? 
                sprintf(_n('%d product in favorites', '%d products in favorites', $total, 'wp-favorites'), $total) :
                __('Your favorites list is empty', 'wp-favorites')
        ));
    }
    
    /**
     * Clear all favorites
     */
    public function clear_all_favorites() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'wp_favorites_nonce')) {
            wp_send_json_error(array(
                'message' => __('Security check failed', 'wp-favorites')
            ));
        }
        
        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error(array(
                'message' => __('Please log in to use favorites', 'wp-favorites'),
                'code' => 'login_required'
            ));
        }
        
        $user_id = get_current_user_id();
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'wp_favorites';
        
        $result = $wpdb->delete(
            $table_name,
            array('user_id' => $user_id),
            array('%d')
        );
        
        if ($result !== false) {
            // Clear cache
            $this->clear_user_cache($user_id);
            
            wp_send_json_success(array(
                'message' => __('All favorites cleared successfully', 'wp-favorites'),
                'cleared_count' => $result
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to clear favorites', 'wp-favorites'),
                'code' => 'clear_failed'
            ));
        }
    }
    
    /**
     * Require login response
     */
    public function require_login() {
        wp_send_json_error(array(
            'message' => __('Please log in to use favorites', 'wp-favorites'),
            'code' => 'login_required',
            'login_url' => wp_login_url(get_permalink())
        ));
    }
    
    /**
     * Clear user cache
     */
    private function clear_user_cache($user_id) {
        // Clear object cache if available
        if (function_exists('wp_cache_delete')) {
            wp_cache_delete("user_favorites_{$user_id}", 'wp_favorites');
        }
        
        // Clear transients
        delete_transient("wp_favorites_user_{$user_id}");
        delete_transient("wp_favorites_count_{$user_id}");
    }
    
    /**
     * Validate product exists and is published
     */
    private function validate_product($product_id) {
        $product = wc_get_product($product_id);
        
        if (!$product) {
            return false;
        }
        
        // Check if product is published
        if ($product->get_status() !== 'publish') {
            return false;
        }
        
        return true;
    }
    
    /**
     * Rate limiting check
     */
    private function check_rate_limit($user_id, $action = 'toggle') {
        $transient_key = "wp_favorites_rate_limit_{$user_id}_{$action}";
        $last_action = get_transient($transient_key);
        
        if ($last_action) {
            return false; // Rate limited
        }
        
        // Set rate limit (1 second)
        set_transient($transient_key, time(), 1);
        return true;
    }
}
