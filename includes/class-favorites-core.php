<?php
/**
 * WP Favorites Core Class
 * 
 * Main functionality and coordination between components
 */

if (!defined('ABSPATH')) {
    exit;
}

class WP_Favorites_Core {
    
    /**
     * Single instance
     */
    private static $instance = null;
    
    /**
     * Admin instance
     */
    public $admin;
    
    /**
     * Frontend instance
     */
    public $frontend;
    
    /**
     * AJAX instance
     */
    public $ajax;
    
    /**
     * Cache instance
     */
    public $cache;
    
    /**
     * Performance instance
     */
    public $performance;
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        // Components will be initialized via the 'init' hook
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Initialize components
        add_action('init', array($this, 'init_components'), 10);

        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));

        // AJAX hooks are handled by the AJAX class to avoid duplication

        // Initialize WooCommerce hooks based on admin settings
        add_action('init', array($this, 'init_woocommerce_hooks'), 20);

        // Shortcode
        add_shortcode('wp_favorites_list', array($this, 'favorites_list_shortcode'));
    }

    /**
     * Initialize WooCommerce hooks based on admin settings
     */
    public function init_woocommerce_hooks() {
        if (!function_exists('WC')) {
            return;
        }

        // Get display location settings
        $display_locations = get_option('wp_favorites_display_locations', array(
            'shop_loop' => true,
            'single_product' => true,
            'related_products' => true,
            'cross_sells' => true,
            'up_sells' => true,
            'widgets' => true,
            'search_results' => true,
            'category_pages' => true
        ));

        // Use a single, unified approach for all product displays
        $this->register_unified_product_hooks($display_locations);
    }
    
    /**
     * Initialize components
     */
    public function init_components() {
        // Initialize cache
        if (class_exists('WP_Favorites_Cache')) {
            $this->cache = new WP_Favorites_Cache();
        }
        
        // Initialize performance
        if (class_exists('WP_Favorites_Performance')) {
            $this->performance = new WP_Favorites_Performance();
        }
        
        // Admin is initialized in main plugin file to avoid duplication
        // $this->admin will be set externally if needed
        
        // Initialize frontend
        if (!is_admin() && class_exists('WP_Favorites_Frontend')) {
            $this->frontend = new WP_Favorites_Frontend();
        }
        
        // Initialize AJAX
        if (class_exists('WP_Favorites_Ajax')) {
            $this->ajax = new WP_Favorites_Ajax();
        }
    }

    /**
     * Register unified product hooks to prevent duplicates
     */
    private function register_unified_product_hooks($display_locations) {
        // Product loops - use single hook for all contexts
        if (!empty($display_locations['shop_loop']) ||
            !empty($display_locations['category_pages']) ||
            !empty($display_locations['related_products']) ||
            !empty($display_locations['cross_sells']) ||
            !empty($display_locations['up_sells']) ||
            !empty($display_locations['search_results'])) {

            // Hook after the product link/image for better positioning
            add_action('woocommerce_before_shop_loop_item_title', array($this, 'add_unified_favorite_button'), 15);
        }

        // Single product pages
        if (!empty($display_locations['single_product'])) {
            add_action('woocommerce_single_product_summary', array($this, 'add_unified_favorite_button'), 35);
        }

        // Widgets
        if (!empty($display_locations['widgets'])) {
            add_action('woocommerce_widget_product_item_start', array($this, 'add_unified_favorite_button'), 10);
        }

        // Setup context detection
        add_action('woocommerce_before_shop_loop_item', array($this, 'setup_product_loop_context'), 5);
    }

    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        // Only load on WooCommerce pages or favorites page
        if (!$this->should_load_frontend_assets()) {
            return;
        }
        
        // CSS
        wp_enqueue_style(
            'wp-favorites-frontend',
            WP_FAVORITES_PLUGIN_URL . 'assets/css/frontend-style.css',
            array(),
            WP_FAVORITES_VERSION
        );
        
        // JavaScript
        wp_enqueue_script(
            'wp-favorites-frontend',
            WP_FAVORITES_PLUGIN_URL . 'assets/js/frontend-script.js',
            array('jquery', 'wp-i18n'),
            WP_FAVORITES_VERSION,
            true
        );
        
        // Prepare localization data
        $localize_data = array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wp_favorites_nonce'),
            'messages' => $this->get_frontend_messages(),
            'settings' => array(
                'notification_duration' => get_option('wp_favorites_notification_duration', 3000),
                'enable_notifications' => get_option('wp_favorites_enable_notifications', true),
            )
        );

        // Apply filter to allow frontend class to add user data
        $localize_data = apply_filters('wp_favorites_localize_data', $localize_data);

        // Localize script with translatable messages
        wp_localize_script('wp-favorites-frontend', 'wpFavoritesData', $localize_data);
        
        // Set up script translations
        wp_set_script_translations('wp-favorites-frontend', 'wp-favorites');
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only load on plugin admin pages
        if (strpos($hook, 'wp-favorites') === false) {
            return;
        }
        
        // CSS
        wp_enqueue_style(
            'wp-favorites-admin',
            WP_FAVORITES_PLUGIN_URL . 'admin/css/admin-style.css',
            array(),
            WP_FAVORITES_VERSION
        );
        
        // JavaScript
        wp_enqueue_script(
            'wp-favorites-admin',
            WP_FAVORITES_PLUGIN_URL . 'admin/js/admin-script.js',
            array('jquery', 'wp-i18n'),
            WP_FAVORITES_VERSION,
            true
        );
        
        // Media uploader
        wp_enqueue_media();
    }
    
    /**
     * Check if frontend assets should be loaded
     */
    private function should_load_frontend_assets() {
        // Only load if WooCommerce is active
        if (!function_exists('WC')) {
            return false;
        }

        // WooCommerce pages
        if (function_exists('is_woocommerce') && is_woocommerce()) {
            return true;
        }

        // Shop page
        if (function_exists('is_shop') && is_shop()) {
            return true;
        }

        // Product category/tag pages
        if (function_exists('is_product_category') && is_product_category()) {
            return true;
        }

        if (function_exists('is_product_tag') && is_product_tag()) {
            return true;
        }

        // Cart page (for cross-sells)
        if (function_exists('is_cart') && is_cart()) {
            return true;
        }

        // Favorites page
        $favorites_page_id = get_option('wp_favorites_page_id', 0);
        if ($favorites_page_id && is_page($favorites_page_id)) {
            return true;
        }

        // Pages with favorites shortcode
        global $post;
        if ($post && has_shortcode($post->post_content, 'wp_favorites_list')) {
            return true;
        }

        // Check if page has WooCommerce shortcodes that might display products
        if ($post && (
            has_shortcode($post->post_content, 'products') ||
            has_shortcode($post->post_content, 'recent_products') ||
            has_shortcode($post->post_content, 'featured_products') ||
            has_shortcode($post->post_content, 'sale_products') ||
            has_shortcode($post->post_content, 'best_selling_products') ||
            has_shortcode($post->post_content, 'top_rated_products') ||
            has_shortcode($post->post_content, 'product_category') ||
            has_shortcode($post->post_content, 'product_categories')
        )) {
            return true;
        }

        // Don't load on homepage unless it's the shop page or has WooCommerce content
        if (is_home() || is_front_page()) {
            $shop_page_id = function_exists('wc_get_page_id') ? wc_get_page_id('shop') : 0;
            if ($shop_page_id && (is_page($shop_page_id) || get_the_ID() === $shop_page_id)) {
                return true;
            }
            // Check if homepage has WooCommerce shortcodes
            if ($post && (
                has_shortcode($post->post_content, 'products') ||
                has_shortcode($post->post_content, 'recent_products') ||
                has_shortcode($post->post_content, 'featured_products')
            )) {
                return true;
            }
            return false;
        }

        return false;
    }
    
    /**
     * Get frontend translatable messages
     */
    private function get_frontend_messages() {
        return array(
            'success' => array(
                'added' => __('Product added to favorites', 'wp-favorites'),
                'removed' => __('Product removed from favorites', 'wp-favorites'),
                'updated' => __('Favorites updated successfully', 'wp-favorites')
            ),
            'error' => array(
                'add_failed' => __('Failed to add product to favorites', 'wp-favorites'),
                'remove_failed' => __('Failed to remove product from favorites', 'wp-favorites'),
                'not_found' => __('Product not found', 'wp-favorites'),
                'login_required' => __('Please log in to use favorites', 'wp-favorites')
            ),
            'info' => array(
                'empty_list' => __('Your favorites list is empty', 'wp-favorites'),
                'loading' => __('Loading favorites...', 'wp-favorites'),
                'no_products' => __('No products match your criteria', 'wp-favorites')
            ),
            'confirm' => array(
                'remove' => __('Are you sure you want to remove this product from favorites?', 'wp-favorites'),
                'clear_all' => __('Are you sure you want to clear all favorites?', 'wp-favorites')
            )
        );
    }
    
    /**
     * Unified method to add favorite button (prevents duplicates)
     */
    public function add_unified_favorite_button() {
        global $product;

        if (!$product || !is_a($product, 'WC_Product')) {
            return;
        }

        $product_id = $product->get_id();

        // Prevent duplicate buttons on the same product in the same request
        static $added_buttons = array();
        if (isset($added_buttons[$product_id])) {
            return;
        }
        $added_buttons[$product_id] = true;

        // Get positioning and size settings
        $position = get_option('wp_favorites_icon_position', 'top-right');
        $size = get_option('wp_favorites_icon_size', 'medium');
        $context = $this->get_current_product_context();

        $user_id = get_current_user_id();
        $is_favorite = $this->is_favorite($user_id, $product_id);

        // Use consistent classes for all contexts
        $button_class = 'wp-favorites-button';
        $button_class .= $is_favorite ? ' is-favorite' : '';
        $button_class .= ' position-' . esc_attr($position);
        $button_class .= ' size-' . esc_attr($size);

        $icon_html = $this->get_favorite_icon($is_favorite);
        $title = $is_favorite
            ? __('Remove from favorites', 'wp-favorites')
            : __('Add to favorites', 'wp-favorites');

        // Context-aware wrapper with proper classes
        $wrapper_class = 'wp-favorites-wrapper';
        $wrapper_class .= ' wp-favorites-context-' . esc_attr($context);

        echo sprintf('<div class="%s">', esc_attr($wrapper_class));
        echo sprintf(
            '<button class="%s" data-product-id="%d" title="%s" aria-label="%s" data-context="%s">%s</button>',
            esc_attr($button_class),
            esc_attr($product_id),
            esc_attr($title),
            esc_attr($title),
            esc_attr($context),
            $icon_html
        );
        echo '</div>';

        // Debug info (only for logged-in admins)
        if (current_user_can('manage_options') && isset($_GET['debug_favorites'])) {
            echo sprintf(
                '<!-- WP Favorites Debug: Product ID: %d, Context: %s, Position: %s, Size: %s -->',
                $product_id,
                $context,
                $position,
                $size
            );
        }
    }

    /**
     * Legacy method for backward compatibility
     */
    public function add_favorite_button_with_wrapper() {
        $this->add_unified_favorite_button();
    }

    /**
     * Legacy method for backward compatibility
     */
    public function add_favorite_button() {
        $this->add_favorite_button_with_wrapper();
    }

    /**
     * Get current product display context
     */
    private function get_current_product_context() {
        // Check for widget context first (most specific)
        if ($this->is_widget_context()) {
            return 'widget';
        }

        // Check for specific WooCommerce page types
        if (is_single() && is_product()) {
            // Check if we're in related/up-sell context within single product
            if ($this->is_related_products_context()) {
                return 'related';
            } elseif ($this->is_up_sell_context()) {
                return 'up-sell';
            }
            return 'single-product';
        } elseif (is_shop()) {
            return 'shop';
        } elseif (is_product_category()) {
            return 'category';
        } elseif (is_product_tag()) {
            return 'tag';
        } elseif (is_search()) {
            return 'search';
        } elseif (is_cart()) {
            // Check if we're in cross-sell context within cart
            if ($this->is_cross_sell_context()) {
                return 'cross-sell';
            }
            return 'cart';
        }

        // Fallback: check action context
        if (doing_action('woocommerce_output_related_products_args') ||
            doing_action('woocommerce_single_product_summary')) {
            return 'related';
        } elseif (doing_action('woocommerce_cart_collaterals') ||
                  doing_action('woocommerce_before_cart_collaterals')) {
            return 'cross-sell';
        } elseif (doing_action('woocommerce_after_single_product_summary')) {
            return 'up-sell';
        }

        return 'general';
    }

    /**
     * Check if we're in a widget context
     */
    private function is_widget_context() {
        // Check if we're in a widget by looking at the call stack
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 15);

        foreach ($backtrace as $trace) {
            if (isset($trace['class']) &&
                (strpos($trace['class'], 'Widget') !== false ||
                 strpos($trace['class'], 'WC_Widget') !== false)) {
                return true;
            }

            if (isset($trace['function']) &&
                (strpos($trace['function'], 'widget') !== false ||
                 $trace['function'] === 'dynamic_sidebar')) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if we're in related products context
     */
    private function is_related_products_context() {
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 15);

        foreach ($backtrace as $trace) {
            if (isset($trace['function']) &&
                (strpos($trace['function'], 'related') !== false ||
                 $trace['function'] === 'woocommerce_output_related_products_args')) {
                return true;
            }

            if (isset($trace['class']) &&
                strpos($trace['class'], 'Related') !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if we're in up-sell products context
     */
    private function is_up_sell_context() {
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 15);

        foreach ($backtrace as $trace) {
            if (isset($trace['function']) &&
                (strpos($trace['function'], 'upsell') !== false ||
                 strpos($trace['function'], 'up_sell') !== false)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if we're in cross-sell products context
     */
    private function is_cross_sell_context() {
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 15);

        foreach ($backtrace as $trace) {
            if (isset($trace['function']) &&
                (strpos($trace['function'], 'cross') !== false ||
                 strpos($trace['function'], 'cross_sell') !== false)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Setup product loop context for better positioning
     */
    public function setup_product_loop_context() {
        // Add CSS class to product loop items for context-aware styling
        add_filter('woocommerce_post_class', array($this, 'add_product_context_class'), 10, 2);
    }

    /**
     * Add context class to product items
     */
    public function add_product_context_class($classes, $product) {
        $context = $this->get_current_product_context();
        $classes[] = 'wp-favorites-context-' . $context;
        return $classes;
    }
    
    /**
     * Check if product is favorite
     */
    public function is_favorite($user_id, $product_id) {
        if (!$user_id || !$product_id) {
            return false;
        }
        
        // Check cache first
        if ($this->cache) {
            $cached = $this->cache->get_user_favorites($user_id);
            if ($cached !== false) {
                return in_array($product_id, $cached);
            }
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'wp_favorites';
        
        $result = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE user_id = %d AND product_id = %d",
            $user_id,
            $product_id
        ));
        
        return $result > 0;
    }
    
    /**
     * Add favorite button for widget products (legacy compatibility)
     */
    public function add_widget_favorite_button() {
        $this->add_unified_favorite_button();
    }

    /**
     * Get favorite icon HTML
     */
    private function get_favorite_icon($is_favorite = false) {
        $icon_type = $is_favorite ? 'filled' : 'empty';

        // Get custom icon if set
        $custom_icon = get_option('wp_favorites_custom_icon', '');
        if (!empty($custom_icon)) {
            return $custom_icon;
        }

        // Default heart icons (can be customized via admin)
        $icons = array(
            'empty' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path></svg>',
            'filled' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path></svg>'
        );

        return apply_filters('wp_favorites_icon_html', $icons[$icon_type], $icon_type, $is_favorite);
    }
    
    /**
     * Favorites list shortcode
     */
    public function favorites_list_shortcode($atts) {
        $atts = shortcode_atts(array(
            'columns' => 4,
            'limit' => -1,
            'show_remove' => 'yes'
        ), $atts, 'wp_favorites_list');
        
        if (!is_user_logged_in()) {
            return '<p>' . __('Please log in to view your favorites.', 'wp-favorites') . '</p>';
        }
        
        ob_start();
        
        if ($this->frontend && method_exists($this->frontend, 'render_favorites_list')) {
            $this->frontend->render_favorites_list($atts);
        }
        
        return ob_get_clean();
    }
    

}
