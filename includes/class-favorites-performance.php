<?php
/**
 * WP Favorites Performance Class
 * 
 * Handles performance optimizations and monitoring
 */

if (!defined('ABSPATH')) {
    exit;
}

class WP_Favorites_Performance {
    
    /**
     * Performance metrics
     */
    private $metrics = array();
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Database optimization
        add_action('wp_favorites_daily_maintenance', array($this, 'optimize_database'));
        
        // Asset optimization
        add_filter('wp_favorites_enqueue_scripts', array($this, 'optimize_assets'));
        
        // Query optimization
        add_action('pre_get_posts', array($this, 'optimize_queries'));
        
        // Performance monitoring
        add_action('wp_favorites_action_start', array($this, 'start_performance_timer'));
        add_action('wp_favorites_action_end', array($this, 'end_performance_timer'));
        
        // Schedule daily maintenance
        if (!wp_next_scheduled('wp_favorites_daily_maintenance')) {
            wp_schedule_event(time(), 'daily', 'wp_favorites_daily_maintenance');
        }
        
        // AJAX optimization
        add_action('wp_ajax_wp_favorites_toggle', array($this, 'optimize_ajax_response'), 1);
        add_action('wp_ajax_nopriv_wp_favorites_toggle', array($this, 'optimize_ajax_response'), 1);
    }
    
    /**
     * Optimize database tables
     */
    public function optimize_database() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'wp_favorites';
        
        // Remove orphaned favorites (products that no longer exist)
        $deleted = $wpdb->query(
            "DELETE f FROM $table_name f 
             LEFT JOIN {$wpdb->posts} p ON f.product_id = p.ID 
             WHERE p.ID IS NULL OR p.post_status != 'publish'"
        );
        
        // Remove favorites for deleted users
        $deleted_users = $wpdb->query(
            "DELETE f FROM $table_name f 
             LEFT JOIN {$wpdb->users} u ON f.user_id = u.ID 
             WHERE u.ID IS NULL"
        );
        
        // Optimize table
        $wpdb->query("OPTIMIZE TABLE $table_name");
        
        // Update statistics
        update_option('wp_favorites_last_cleanup', array(
            'date' => current_time('mysql'),
            'orphaned_removed' => $deleted,
            'user_cleanup' => $deleted_users
        ));
        
        return array(
            'orphaned_removed' => $deleted,
            'users_cleaned' => $deleted_users
        );
    }
    
    /**
     * Optimize asset loading
     */
    public function optimize_assets($should_enqueue) {
        // Don't load assets on admin pages (except plugin pages)
        if (is_admin() && !$this->is_plugin_admin_page()) {
            return false;
        }
        
        // Don't load on non-WooCommerce pages unless favorites page
        if (!$this->should_load_assets()) {
            return false;
        }
        
        return $should_enqueue;
    }
    
    /**
     * Optimize database queries
     */
    public function optimize_queries($query) {
        // Skip if not main query or admin
        if (!$query->is_main_query() || is_admin()) {
            return;
        }
        
        // Optimize WooCommerce product queries
        if ($query->is_shop() || $query->is_product_category() || $query->is_product_tag()) {
            // Add favorites data efficiently
            add_action('woocommerce_shop_loop_item_title', array($this, 'preload_favorites_data'), 5);
        }
    }
    
    /**
     * Preload favorites data for shop loops
     */
    public function preload_favorites_data() {
        if (!is_user_logged_in()) {
            return;
        }
        
        static $preloaded = false;
        if ($preloaded) {
            return;
        }
        
        $user_id = get_current_user_id();
        
        // Get all product IDs in current loop
        global $woocommerce_loop;
        if (isset($woocommerce_loop['is_shortcode']) && $woocommerce_loop['is_shortcode']) {
            return; // Skip for shortcodes
        }
        
        // Preload user favorites
        $this->preload_user_favorites($user_id);
        
        $preloaded = true;
    }
    
    /**
     * Preload user favorites to reduce queries
     */
    private function preload_user_favorites($user_id) {
        global $wpdb;
        
        // Check cache first
        $cache_key = "user_favorites_{$user_id}";
        $favorites = wp_cache_get($cache_key, 'wp_favorites');
        
        if ($favorites === false) {
            $table_name = $wpdb->prefix . 'wp_favorites';
            $favorites = $wpdb->get_col($wpdb->prepare(
                "SELECT product_id FROM $table_name WHERE user_id = %d",
                $user_id
            ));
            
            // Cache for this request
            wp_cache_set($cache_key, $favorites, 'wp_favorites', 300);
        }
        
        return $favorites;
    }
    
    /**
     * Optimize AJAX responses
     */
    public function optimize_ajax_response() {
        // Disable unnecessary WordPress features for AJAX
        remove_action('wp_head', 'wp_generator');
        remove_action('wp_head', 'wlwmanifest_link');
        remove_action('wp_head', 'rsd_link');
        
        // Set proper headers for caching
        if (!headers_sent()) {
            header('Cache-Control: no-cache, must-revalidate, max-age=0');
            header('Pragma: no-cache');
            header('Expires: Wed, 11 Jan 1984 05:00:00 GMT');
        }
    }
    
    /**
     * Start performance timer
     */
    public function start_performance_timer($action) {
        $this->metrics[$action] = array(
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true)
        );
    }
    
    /**
     * End performance timer
     */
    public function end_performance_timer($action) {
        if (!isset($this->metrics[$action])) {
            return;
        }
        
        $this->metrics[$action]['end_time'] = microtime(true);
        $this->metrics[$action]['end_memory'] = memory_get_usage(true);
        $this->metrics[$action]['execution_time'] = $this->metrics[$action]['end_time'] - $this->metrics[$action]['start_time'];
        $this->metrics[$action]['memory_usage'] = $this->metrics[$action]['end_memory'] - $this->metrics[$action]['start_memory'];
        
        // Log slow operations
        if ($this->metrics[$action]['execution_time'] > 1.0) {
            error_log("WP Favorites: Slow operation detected - {$action} took {$this->metrics[$action]['execution_time']} seconds");
        }
    }
    
    /**
     * Get performance metrics
     */
    public function get_performance_metrics() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'wp_favorites';
        
        // Database metrics
        $total_favorites = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        $unique_users = $wpdb->get_var("SELECT COUNT(DISTINCT user_id) FROM $table_name");
        $unique_products = $wpdb->get_var("SELECT COUNT(DISTINCT product_id) FROM $table_name");
        
        // Table size
        $table_size = $wpdb->get_row(
            "SELECT 
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'size_mb',
                table_rows
             FROM information_schema.TABLES 
             WHERE table_schema = DATABASE() 
             AND table_name = '$table_name'"
        );
        
        // Cache metrics
        $cache_stats = array();
        if (class_exists('WP_Favorites_Cache')) {
            $cache = new WP_Favorites_Cache();
            $cache_stats = $cache->get_cache_stats();
        }
        
        return array(
            'database' => array(
                'total_favorites' => intval($total_favorites),
                'unique_users' => intval($unique_users),
                'unique_products' => intval($unique_products),
                'table_size_mb' => $table_size ? floatval($table_size->size_mb) : 0,
                'table_rows' => $table_size ? intval($table_size->table_rows) : 0
            ),
            'cache' => $cache_stats,
            'performance' => $this->metrics,
            'last_cleanup' => get_option('wp_favorites_last_cleanup', array())
        );
    }
    
    /**
     * Check if should load assets
     */
    private function should_load_assets() {
        // WooCommerce pages
        if (function_exists('is_woocommerce') && is_woocommerce()) {
            return true;
        }
        
        // Shop page
        if (function_exists('is_shop') && is_shop()) {
            return true;
        }
        
        // Favorites page
        $favorites_page_id = get_option('wp_favorites_page_id', 0);
        if ($favorites_page_id && is_page($favorites_page_id)) {
            return true;
        }
        
        // Pages with shortcode
        global $post;
        if ($post && has_shortcode($post->post_content, 'wp_favorites_list')) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if current admin page is plugin page
     */
    private function is_plugin_admin_page() {
        $screen = get_current_screen();
        return $screen && strpos($screen->id, 'wp-favorites') !== false;
    }
    
    /**
     * Optimize database indexes
     */
    public function optimize_indexes() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'wp_favorites';
        
        // Check existing indexes
        $indexes = $wpdb->get_results("SHOW INDEX FROM $table_name");
        $existing_indexes = array();
        
        foreach ($indexes as $index) {
            $existing_indexes[] = $index->Key_name;
        }
        
        // Add performance indexes if they don't exist
        $performance_indexes = array(
            'idx_user_date' => "ALTER TABLE $table_name ADD INDEX idx_user_date (user_id, date_added)",
            'idx_product_date' => "ALTER TABLE $table_name ADD INDEX idx_product_date (product_id, date_added)"
        );
        
        foreach ($performance_indexes as $index_name => $sql) {
            if (!in_array($index_name, $existing_indexes)) {
                $wpdb->query($sql);
            }
        }
    }
    
    /**
     * Get slow query log
     */
    public function get_slow_queries() {
        $slow_queries = get_option('wp_favorites_slow_queries', array());
        
        // Keep only last 50 entries
        if (count($slow_queries) > 50) {
            $slow_queries = array_slice($slow_queries, -50);
            update_option('wp_favorites_slow_queries', $slow_queries);
        }
        
        return $slow_queries;
    }
    
    /**
     * Log slow query
     */
    public function log_slow_query($query, $execution_time) {
        if ($execution_time < 0.5) {
            return; // Only log queries slower than 500ms
        }
        
        $slow_queries = get_option('wp_favorites_slow_queries', array());
        $slow_queries[] = array(
            'query' => $query,
            'execution_time' => $execution_time,
            'timestamp' => current_time('mysql'),
            'memory_usage' => memory_get_usage(true)
        );
        
        update_option('wp_favorites_slow_queries', $slow_queries);
    }
}
