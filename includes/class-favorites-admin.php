<?php
/**
 * WP Favorites Admin Class
 * 
 * Handles admin interface and settings
 */

if (!defined('ABSPATH')) {
    exit;
}

class WP_Favorites_Admin {

    /**
     * Instance counter for debugging
     */
    private static $instance_count = 0;

    /**
     * Constructor
     */
    public function __construct() {
        self::$instance_count++;
        if (self::$instance_count > 1) {
            error_log('WARNING: WP_Favorites_Admin instantiated multiple times (' . self::$instance_count . ')');
        }
        $this->init_hooks();
    }
    
    /**
     * Get instance count (for debugging)
     */
    public static function get_instance_count() {
        return self::$instance_count;
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));

        // Settings
        add_action('admin_init', array($this, 'register_settings'));

        // AJAX handlers for admin
        add_action('wp_ajax_wp_favorites_upload_icon', array($this, 'handle_icon_upload'));
        add_action('wp_ajax_wp_favorites_save_translation', array($this, 'handle_save_translation'));
    }
    
    /**
     * Add admin menu - Top level in sidebar
     */
    public function add_admin_menu() {
        // Main menu page (top-level in sidebar)
        add_menu_page(
            __('WP Favorites', 'wp-favorites'),           // Page title
            __('Favorites', 'wp-favorites'),              // Menu title
            'manage_options',                             // Capability
            'wp-favorites',                               // Menu slug
            array($this, 'admin_page'),                   // Callback
            'dashicons-heart',                            // Icon
            30                                            // Position (high priority)
        );

        // Submenu pages for better organization
        add_submenu_page(
            'wp-favorites',
            __('Performance Settings', 'wp-favorites'),
            __('Performance', 'wp-favorites'),
            'manage_options',
            'wp-favorites-performance',
            array($this, 'performance_page')
        );

        add_submenu_page(
            'wp-favorites',
            __('Translation Management', 'wp-favorites'),
            __('Translations', 'wp-favorites'),
            'manage_options',
            'wp-favorites-translations',
            array($this, 'translations_page')
        );
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        // General settings
        register_setting('wp_favorites_general', 'wp_favorites_page_id');
        register_setting('wp_favorites_general', 'wp_favorites_icon_position');
        register_setting('wp_favorites_general', 'wp_favorites_icon_size');
        register_setting('wp_favorites_general', 'wp_favorites_custom_icon');
        register_setting('wp_favorites_general', 'wp_favorites_display_locations');

        // Performance settings
        register_setting('wp_favorites_performance', 'wp_favorites_enable_cache');
        register_setting('wp_favorites_performance', 'wp_favorites_cache_duration');
        register_setting('wp_favorites_performance', 'wp_favorites_enable_notifications');
        register_setting('wp_favorites_performance', 'wp_favorites_notification_duration');

        // Translation settings
        register_setting('wp_favorites_translations', 'wp_favorites_custom_messages');
    }
    
    /**
     * Main admin page
     */
    public function admin_page() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        // Determine active tab based on current page
        $current_page = isset($_GET['page']) ? sanitize_text_field($_GET['page']) : 'wp-favorites';
        $active_tab = 'general'; // default for main page
        ?>
        <div class="wrap wp-favorites-admin">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <nav class="nav-tab-wrapper">
                <a href="?page=wp-favorites"
                   class="nav-tab <?php echo $active_tab === 'general' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('General Settings', 'wp-favorites'); ?>
                </a>
                <a href="?page=wp-favorites-performance"
                   class="nav-tab <?php echo $active_tab === 'performance' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('Performance', 'wp-favorites'); ?>
                </a>
                <a href="?page=wp-favorites-translations"
                   class="nav-tab <?php echo $active_tab === 'translations' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('Translations', 'wp-favorites'); ?>
                </a>
            </nav>
            
            <div class="tab-content">
                <?php $this->render_general_settings(); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Performance settings page
     */
    public function performance_page() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        ?>
        <div class="wrap wp-favorites-admin">
            <h1><?php _e('Performance Settings', 'wp-favorites'); ?></h1>

            <nav class="nav-tab-wrapper">
                <a href="?page=wp-favorites" class="nav-tab">
                    <?php _e('General Settings', 'wp-favorites'); ?>
                </a>
                <a href="?page=wp-favorites-performance" class="nav-tab nav-tab-active">
                    <?php _e('Performance', 'wp-favorites'); ?>
                </a>
                <a href="?page=wp-favorites-translations" class="nav-tab">
                    <?php _e('Translations', 'wp-favorites'); ?>
                </a>
            </nav>

            <form method="post" action="options.php">
                <?php
                settings_fields('wp_favorites_performance');
                do_settings_sections('wp_favorites_performance');
                ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Enable Caching', 'wp-favorites'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="wp_favorites_enable_cache" value="1" 
                                       <?php checked(get_option('wp_favorites_enable_cache', true)); ?> />
                                <?php _e('Enable object caching for better performance', 'wp-favorites'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Cache Duration', 'wp-favorites'); ?></th>
                        <td>
                            <input type="number" name="wp_favorites_cache_duration" 
                                   value="<?php echo esc_attr(get_option('wp_favorites_cache_duration', 3600)); ?>" 
                                   min="300" max="86400" />
                            <p class="description">
                                <?php _e('Cache duration in seconds (300-86400)', 'wp-favorites'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('User Notifications', 'wp-favorites'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="wp_favorites_enable_notifications" value="1" 
                                       <?php checked(get_option('wp_favorites_enable_notifications', true)); ?> />
                                <?php _e('Enable toast notifications for user actions', 'wp-favorites'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Notification Duration', 'wp-favorites'); ?></th>
                        <td>
                            <input type="number" name="wp_favorites_notification_duration" 
                                   value="<?php echo esc_attr(get_option('wp_favorites_notification_duration', 3000)); ?>" 
                                   min="1000" max="10000" />
                            <p class="description">
                                <?php _e('Notification display duration in milliseconds', 'wp-favorites'); ?>
                            </p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(); ?>
            </form>
        </div>
        <?php
    }
    
    /**
     * Translations management page
     */
    public function translations_page() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        $messages = $this->get_translatable_messages();
        $custom_messages = get_option('wp_favorites_custom_messages', array());
        ?>
        <div class="wrap wp-favorites-admin">
            <h1><?php _e('Translation Management', 'wp-favorites'); ?></h1>

            <nav class="nav-tab-wrapper">
                <a href="?page=wp-favorites" class="nav-tab">
                    <?php _e('General Settings', 'wp-favorites'); ?>
                </a>
                <a href="?page=wp-favorites-performance" class="nav-tab">
                    <?php _e('Performance', 'wp-favorites'); ?>
                </a>
                <a href="?page=wp-favorites-translations" class="nav-tab nav-tab-active">
                    <?php _e('Translations', 'wp-favorites'); ?>
                </a>
            </nav>

            <p class="description">
                <?php _e('Customize frontend user messages and notifications. These will override the default translations.', 'wp-favorites'); ?>
            </p>

            <form method="post" action="options.php">
                <?php
                settings_fields('wp_favorites_translations');
                do_settings_sections('wp_favorites_translations');
                ?>
                
                <div class="translation-sections">
                    <?php foreach ($messages as $category => $category_messages): ?>
                        <div class="translation-category">
                            <h3><?php echo esc_html(ucfirst($category)); ?> <?php _e('Messages', 'wp-favorites'); ?></h3>
                            
                            <table class="form-table">
                                <?php foreach ($category_messages as $key => $default_text): ?>
                                    <tr>
                                        <th scope="row">
                                            <label for="message_<?php echo esc_attr($category . '_' . $key); ?>">
                                                <?php echo esc_html($default_text); ?>
                                            </label>
                                            <p class="description">
                                                <?php printf(__('Key: %s', 'wp-favorites'), '<code>' . $category . '.' . $key . '</code>'); ?>
                                            </p>
                                        </th>
                                        <td>
                                            <input type="text" 
                                                   id="message_<?php echo esc_attr($category . '_' . $key); ?>"
                                                   name="wp_favorites_custom_messages[<?php echo esc_attr($category); ?>][<?php echo esc_attr($key); ?>]" 
                                                   value="<?php echo esc_attr($custom_messages[$category][$key] ?? ''); ?>" 
                                                   class="regular-text" 
                                                   placeholder="<?php echo esc_attr($default_text); ?>" />
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </table>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <?php submit_button(__('Save Translations', 'wp-favorites')); ?>
            </form>
        </div>
        <?php
    }
    
    /**
     * Render general settings
     */
    private function render_general_settings() {
        ?>
        <form method="post" action="options.php">
            <?php
            settings_fields('wp_favorites_general');
            do_settings_sections('wp_favorites_general');
            ?>
            
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Favorites Page', 'wp-favorites'); ?></th>
                    <td>
                        <?php
                        wp_dropdown_pages(array(
                            'name' => 'wp_favorites_page_id',
                            'selected' => get_option('wp_favorites_page_id', 0),
                            'show_option_none' => __('Select a page', 'wp-favorites'),
                            'option_none_value' => 0
                        ));
                        ?>
                        <p class="description">
                            <?php _e('Select the page where favorites will be displayed', 'wp-favorites'); ?>
                        </p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Icon Position', 'wp-favorites'); ?></th>
                    <td>
                        <select name="wp_favorites_icon_position">
                            <option value="top-right" <?php selected(get_option('wp_favorites_icon_position', 'top-right'), 'top-right'); ?>>
                                <?php _e('Top Right', 'wp-favorites'); ?>
                            </option>
                            <option value="top-left" <?php selected(get_option('wp_favorites_icon_position'), 'top-left'); ?>>
                                <?php _e('Top Left', 'wp-favorites'); ?>
                            </option>
                            <option value="bottom-right" <?php selected(get_option('wp_favorites_icon_position'), 'bottom-right'); ?>>
                                <?php _e('Bottom Right', 'wp-favorites'); ?>
                            </option>
                            <option value="bottom-left" <?php selected(get_option('wp_favorites_icon_position'), 'bottom-left'); ?>>
                                <?php _e('Bottom Left', 'wp-favorites'); ?>
                            </option>
                        </select>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Icon Size', 'wp-favorites'); ?></th>
                    <td>
                        <select name="wp_favorites_icon_size">
                            <option value="small" <?php selected(get_option('wp_favorites_icon_size', 'medium'), 'small'); ?>>
                                <?php _e('Small (32px)', 'wp-favorites'); ?>
                            </option>
                            <option value="medium" <?php selected(get_option('wp_favorites_icon_size', 'medium'), 'medium'); ?>>
                                <?php _e('Medium (40px)', 'wp-favorites'); ?>
                            </option>
                            <option value="large" <?php selected(get_option('wp_favorites_icon_size', 'medium'), 'large'); ?>>
                                <?php _e('Large (48px)', 'wp-favorites'); ?>
                            </option>
                        </select>
                    </td>
                </tr>

                <tr>
                    <th scope="row"><?php _e('Custom Icon HTML', 'wp-favorites'); ?></th>
                    <td>
                        <textarea name="wp_favorites_custom_icon" rows="3" cols="50" class="large-text code"><?php echo esc_textarea(get_option('wp_favorites_custom_icon', '')); ?></textarea>
                        <p class="description">
                            <?php _e('Optional: Enter custom HTML for the favorite icon (e.g., Font Awesome icons, custom SVG). Leave empty to use default heart icon.', 'wp-favorites'); ?>
                        </p>
                    </td>
                </tr>
            </table>

            <h3><?php _e('Display Locations', 'wp-favorites'); ?></h3>
            <p><?php _e('Choose where favorite icons should appear on your WooCommerce store:', 'wp-favorites'); ?></p>

            <table class="form-table">
                <?php
                $display_locations = get_option('wp_favorites_display_locations', array(
                    'shop_loop' => true,
                    'single_product' => true,
                    'related_products' => true,
                    'cross_sells' => true,
                    'up_sells' => true,
                    'widgets' => true,
                    'search_results' => true,
                    'category_pages' => true
                ));

                $location_options = array(
                    'shop_loop' => __('Shop Page (Main Product Listing)', 'wp-favorites'),
                    'category_pages' => __('Category & Tag Archive Pages', 'wp-favorites'),
                    'single_product' => __('Single Product Pages', 'wp-favorites'),
                    'related_products' => __('Related Products Section', 'wp-favorites'),
                    'cross_sells' => __('Cross-sell Products (Cart Page)', 'wp-favorites'),
                    'up_sells' => __('Up-sell Products (Product Page)', 'wp-favorites'),
                    'widgets' => __('Product Widgets & Shortcodes', 'wp-favorites'),
                    'search_results' => __('Search Results Pages', 'wp-favorites')
                );
                ?>

                <tr>
                    <th scope="row"><?php _e('Show Favorite Icons On', 'wp-favorites'); ?></th>
                    <td>
                        <fieldset>
                            <legend class="screen-reader-text"><?php _e('Display Locations', 'wp-favorites'); ?></legend>
                            <?php foreach ($location_options as $key => $label): ?>
                                <label>
                                    <input type="checkbox"
                                           name="wp_favorites_display_locations[<?php echo esc_attr($key); ?>]"
                                           value="1"
                                           <?php checked(!empty($display_locations[$key])); ?> />
                                    <?php echo esc_html($label); ?>
                                </label><br>
                            <?php endforeach; ?>
                        </fieldset>
                        <p class="description">
                            <?php _e('Select all locations where you want favorite icons to appear. Changes take effect immediately.', 'wp-favorites'); ?>
                        </p>
                    </td>
                </tr>
            </table>
            
            <?php submit_button(); ?>
        </form>
        <?php
    }
    
    /**
     * Get translatable messages for admin interface
     */
    private function get_translatable_messages() {
        return array(
            'success' => array(
                'added' => __('Product added to favorites', 'wp-favorites'),
                'removed' => __('Product removed from favorites', 'wp-favorites'),
                'updated' => __('Favorites updated successfully', 'wp-favorites')
            ),
            'error' => array(
                'add_failed' => __('Failed to add product to favorites', 'wp-favorites'),
                'remove_failed' => __('Failed to remove product from favorites', 'wp-favorites'),
                'not_found' => __('Product not found', 'wp-favorites'),
                'login_required' => __('Please log in to use favorites', 'wp-favorites')
            ),
            'info' => array(
                'empty_list' => __('Your favorites list is empty', 'wp-favorites'),
                'loading' => __('Loading favorites...', 'wp-favorites'),
                'no_products' => __('No products match your criteria', 'wp-favorites')
            ),
            'confirm' => array(
                'remove' => __('Are you sure you want to remove this product from favorites?', 'wp-favorites'),
                'clear_all' => __('Are you sure you want to clear all favorites?', 'wp-favorites')
            )
        );
    }
    
    /**
     * Handle icon upload
     */
    public function handle_icon_upload() {
        check_ajax_referer('wp_favorites_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'wp-favorites'));
        }
        
        // Handle file upload logic here
        wp_send_json_success(array(
            'message' => __('Icon uploaded successfully', 'wp-favorites')
        ));
    }
    
    /**
     * Handle save translation
     */
    public function handle_save_translation() {
        check_ajax_referer('wp_favorites_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'wp-favorites'));
        }
        
        // Handle translation save logic here
        wp_send_json_success(array(
            'message' => __('Translation saved successfully', 'wp-favorites')
        ));
    }
}
