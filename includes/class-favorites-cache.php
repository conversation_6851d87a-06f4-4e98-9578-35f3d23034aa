<?php
/**
 * WP Favorites Cache Class
 * 
 * Handles caching for better performance
 */

if (!defined('ABSPATH')) {
    exit;
}

class WP_Favorites_Cache {
    
    /**
     * Cache group
     */
    private $cache_group = 'wp_favorites';
    
    /**
     * Cache duration
     */
    private $cache_duration;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->cache_duration = get_option('wp_favorites_cache_duration', 3600);
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Clear cache on user actions
        add_action('wp_favorites_added', array($this, 'clear_user_cache'));
        add_action('wp_favorites_removed', array($this, 'clear_user_cache'));
        
        // Clear cache on product updates
        add_action('woocommerce_update_product', array($this, 'clear_product_cache'));
        add_action('woocommerce_delete_product', array($this, 'clear_product_cache'));
        
        // Scheduled cache cleanup
        add_action('wp_favorites_cache_cleanup', array($this, 'cleanup_expired_cache'));
        
        // Schedule cleanup if not already scheduled
        if (!wp_next_scheduled('wp_favorites_cache_cleanup')) {
            wp_schedule_event(time(), 'daily', 'wp_favorites_cache_cleanup');
        }
    }
    
    /**
     * Get user favorites from cache
     */
    public function get_user_favorites($user_id) {
        if (!$this->is_cache_enabled()) {
            return false;
        }
        
        $cache_key = "user_favorites_{$user_id}";
        
        // Try object cache first
        $cached = wp_cache_get($cache_key, $this->cache_group);
        if ($cached !== false) {
            return $cached;
        }
        
        // Try transient cache
        $cached = get_transient($cache_key);
        if ($cached !== false) {
            // Store in object cache for this request
            wp_cache_set($cache_key, $cached, $this->cache_group, $this->cache_duration);
            return $cached;
        }
        
        return false;
    }
    
    /**
     * Set user favorites in cache
     */
    public function set_user_favorites($user_id, $favorites) {
        if (!$this->is_cache_enabled()) {
            return false;
        }
        
        $cache_key = "user_favorites_{$user_id}";
        
        // Store in object cache
        wp_cache_set($cache_key, $favorites, $this->cache_group, $this->cache_duration);
        
        // Store in transient cache
        set_transient($cache_key, $favorites, $this->cache_duration);
        
        return true;
    }
    
    /**
     * Get user favorites count from cache
     */
    public function get_user_favorites_count($user_id) {
        if (!$this->is_cache_enabled()) {
            return false;
        }
        
        $cache_key = "user_favorites_count_{$user_id}";
        
        // Try object cache first
        $cached = wp_cache_get($cache_key, $this->cache_group);
        if ($cached !== false) {
            return $cached;
        }
        
        // Try transient cache
        $cached = get_transient($cache_key);
        if ($cached !== false) {
            wp_cache_set($cache_key, $cached, $this->cache_group, $this->cache_duration);
            return $cached;
        }
        
        return false;
    }
    
    /**
     * Set user favorites count in cache
     */
    public function set_user_favorites_count($user_id, $count) {
        if (!$this->is_cache_enabled()) {
            return false;
        }
        
        $cache_key = "user_favorites_count_{$user_id}";
        
        // Store in object cache
        wp_cache_set($cache_key, $count, $this->cache_group, $this->cache_duration);
        
        // Store in transient cache
        set_transient($cache_key, $count, $this->cache_duration);
        
        return true;
    }
    
    /**
     * Get product favorites count from cache
     */
    public function get_product_favorites_count($product_id) {
        if (!$this->is_cache_enabled()) {
            return false;
        }
        
        $cache_key = "product_favorites_count_{$product_id}";
        
        // Try object cache first
        $cached = wp_cache_get($cache_key, $this->cache_group);
        if ($cached !== false) {
            return $cached;
        }
        
        // Try transient cache
        $cached = get_transient($cache_key);
        if ($cached !== false) {
            wp_cache_set($cache_key, $cached, $this->cache_group, $this->cache_duration);
            return $cached;
        }
        
        return false;
    }
    
    /**
     * Set product favorites count in cache
     */
    public function set_product_favorites_count($product_id, $count) {
        if (!$this->is_cache_enabled()) {
            return false;
        }
        
        $cache_key = "product_favorites_count_{$product_id}";
        
        // Store in object cache
        wp_cache_set($cache_key, $count, $this->cache_group, $this->cache_duration);
        
        // Store in transient cache
        set_transient($cache_key, $count, $this->cache_duration);
        
        return true;
    }
    
    /**
     * Clear user cache
     */
    public function clear_user_cache($user_id) {
        $cache_keys = array(
            "user_favorites_{$user_id}",
            "user_favorites_count_{$user_id}"
        );
        
        foreach ($cache_keys as $key) {
            // Clear object cache
            wp_cache_delete($key, $this->cache_group);
            
            // Clear transient cache
            delete_transient($key);
        }
        
        // Clear user-specific page cache if using caching plugins
        $this->clear_user_page_cache($user_id);
    }
    
    /**
     * Clear product cache
     */
    public function clear_product_cache($product_id) {
        $cache_key = "product_favorites_count_{$product_id}";
        
        // Clear object cache
        wp_cache_delete($cache_key, $this->cache_group);
        
        // Clear transient cache
        delete_transient($cache_key);
    }
    
    /**
     * Clear all favorites cache
     */
    public function clear_all_cache() {
        // Clear object cache group
        if (function_exists('wp_cache_flush_group')) {
            wp_cache_flush_group($this->cache_group);
        }
        
        // Clear transient cache
        global $wpdb;
        $wpdb->query(
            "DELETE FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_user_favorites_%' 
             OR option_name LIKE '_transient_timeout_user_favorites_%'
             OR option_name LIKE '_transient_product_favorites_%'
             OR option_name LIKE '_transient_timeout_product_favorites_%'"
        );
    }
    
    /**
     * Clear user page cache for caching plugins
     */
    private function clear_user_page_cache($user_id) {
        // Get favorites page ID
        $page_id = get_option('wp_favorites_page_id', 0);
        if (!$page_id) {
            return;
        }
        
        $page_url = get_permalink($page_id);
        
        // Clear WP Rocket cache
        if (function_exists('rocket_clean_post')) {
            rocket_clean_post($page_id);
        }
        
        // Clear W3 Total Cache
        if (function_exists('w3tc_pgcache_flush_post')) {
            w3tc_pgcache_flush_post($page_id);
        }
        
        // Clear WP Super Cache
        if (function_exists('wp_cache_post_change')) {
            wp_cache_post_change($page_id);
        }
        
        // Clear LiteSpeed Cache
        if (class_exists('LiteSpeed_Cache_API')) {
            LiteSpeed_Cache_API::purge_post($page_id);
        }
    }
    
    /**
     * Check if cache is enabled
     */
    private function is_cache_enabled() {
        return get_option('wp_favorites_enable_cache', true);
    }
    
    /**
     * Cleanup expired cache
     */
    public function cleanup_expired_cache() {
        global $wpdb;
        
        // Clean up expired transients
        $wpdb->query(
            "DELETE a, b FROM {$wpdb->options} a, {$wpdb->options} b
             WHERE a.option_name LIKE '_transient_%'
             AND a.option_name NOT LIKE '_transient_timeout_%'
             AND b.option_name = CONCAT('_transient_timeout_', SUBSTRING(a.option_name, 12))
             AND b.option_value < UNIX_TIMESTAMP()
             AND (a.option_name LIKE '_transient_user_favorites_%' 
                  OR a.option_name LIKE '_transient_product_favorites_%')"
        );
    }
    
    /**
     * Get cache statistics
     */
    public function get_cache_stats() {
        global $wpdb;
        
        // Count cached items
        $user_favorites = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_user_favorites_%'"
        );
        
        $product_favorites = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_product_favorites_%'"
        );
        
        return array(
            'user_favorites_cached' => intval($user_favorites),
            'product_favorites_cached' => intval($product_favorites),
            'cache_enabled' => $this->is_cache_enabled(),
            'cache_duration' => $this->cache_duration
        );
    }
    
    /**
     * Warm up cache for popular products
     */
    public function warm_up_cache() {
        global $wpdb;
        
        // Get most favorited products
        $table_name = $wpdb->prefix . 'wp_favorites';
        $popular_products = $wpdb->get_results(
            "SELECT product_id, COUNT(*) as count 
             FROM $table_name 
             GROUP BY product_id 
             ORDER BY count DESC 
             LIMIT 50"
        );
        
        foreach ($popular_products as $product) {
            $this->set_product_favorites_count($product->product_id, $product->count);
        }
        
        return count($popular_products);
    }
}
