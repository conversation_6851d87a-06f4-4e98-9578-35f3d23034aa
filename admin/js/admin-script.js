/**
 * WP Favorites Admin JavaScript
 * Professional admin interface functionality
 */

(function($) {
    'use strict';

    const WPFavoritesAdmin = {
        
        /**
         * Initialize admin functionality
         */
        init: function() {
            this.bindEvents();
            this.initTabs();
            this.initIconUpload();
            this.initTranslationManager();
            this.loadPerformanceStats();
            
            console.log('WP Favorites Admin initialized');
        },
        
        /**
         * Bind event handlers
         */
        bindEvents: function() {
            // Tab navigation
            $('.nav-tab').on('click', this.handleTabClick.bind(this));
            
            // Icon upload
            $('.icon-upload-area').on('click', this.triggerIconUpload.bind(this));
            $('.icon-upload-area').on('dragover', this.handleDragOver.bind(this));
            $('.icon-upload-area').on('drop', this.handleIconDrop.bind(this));
            
            // Icon selection
            $(document).on('click', '.icon-preview-item', this.handleIconSelect.bind(this));
            $(document).on('click', '.remove-icon', this.handleIconRemove.bind(this));
            
            // Translation management
            $('.translation-category input').on('input', this.handleTranslationChange.bind(this));
            $('.save-translations').on('click', this.saveTranslations.bind(this));
            
            // Performance actions
            $('.clear-cache-btn').on('click', this.clearCache.bind(this));
            $('.optimize-db-btn').on('click', this.optimizeDatabase.bind(this));
            
            // Form validation
            $('form').on('submit', this.validateForm.bind(this));
        },
        
        /**
         * Initialize tab functionality
         */
        initTabs: function() {
            const urlParams = new URLSearchParams(window.location.search);
            const activeTab = urlParams.get('tab') || 'general';
            
            this.showTab(activeTab);
        },
        
        /**
         * Handle tab clicks
         */
        handleTabClick: function(e) {
            e.preventDefault();
            
            const $tab = $(e.currentTarget);
            const tabId = $tab.attr('href').split('tab=')[1] || 'general';
            
            this.showTab(tabId);
            
            // Update URL without page reload
            const url = new URL(window.location);
            url.searchParams.set('tab', tabId);
            window.history.pushState({}, '', url);
        },
        
        /**
         * Show specific tab
         */
        showTab: function(tabId) {
            // Update tab navigation
            $('.nav-tab').removeClass('nav-tab-active');
            $(`.nav-tab[href*="tab=${tabId}"]`).addClass('nav-tab-active');
            
            // Show/hide tab content
            $('.tab-content > div').hide();
            $(`.tab-content .tab-${tabId}`).show();
        },
        
        /**
         * Initialize icon upload functionality
         */
        initIconUpload: function() {
            // Create hidden file input
            if (!$('#icon-file-input').length) {
                $('body').append('<input type="file" id="icon-file-input" accept=".svg,.png,.jpg,.jpeg" multiple style="display:none;">');
            }
            
            $('#icon-file-input').on('change', this.handleIconFiles.bind(this));
        },
        
        /**
         * Trigger icon upload
         */
        triggerIconUpload: function(e) {
            e.preventDefault();
            $('#icon-file-input').click();
        },
        
        /**
         * Handle drag over
         */
        handleDragOver: function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(e.currentTarget).addClass('dragover');
        },
        
        /**
         * Handle icon drop
         */
        handleIconDrop: function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const $area = $(e.currentTarget);
            $area.removeClass('dragover');
            
            const files = e.originalEvent.dataTransfer.files;
            this.processIconFiles(files);
        },
        
        /**
         * Handle icon file selection
         */
        handleIconFiles: function(e) {
            const files = e.target.files;
            this.processIconFiles(files);
        },
        
        /**
         * Process icon files
         */
        processIconFiles: function(files) {
            if (!files || files.length === 0) return;
            
            const allowedTypes = ['image/svg+xml', 'image/png', 'image/jpeg', 'image/jpg'];
            const maxSize = 2 * 1024 * 1024; // 2MB
            
            Array.from(files).forEach(file => {
                // Validate file type
                if (!allowedTypes.includes(file.type)) {
                    this.showNotice('error', `Invalid file type: ${file.name}`);
                    return;
                }
                
                // Validate file size
                if (file.size > maxSize) {
                    this.showNotice('error', `File too large: ${file.name} (max 2MB)`);
                    return;
                }
                
                this.uploadIcon(file);
            });
        },
        
        /**
         * Upload icon file
         */
        uploadIcon: function(file) {
            const formData = new FormData();
            formData.append('action', 'wp_favorites_upload_icon');
            formData.append('nonce', wpFavoritesAdmin.nonce);
            formData.append('icon_file', file);
            
            this.showLoading('Uploading icon...');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: (response) => {
                    this.hideLoading();
                    
                    if (response.success) {
                        this.addIconToPreview(response.data);
                        this.showNotice('success', response.data.message);
                    } else {
                        this.showNotice('error', response.data.message || 'Upload failed');
                    }
                },
                error: () => {
                    this.hideLoading();
                    this.showNotice('error', 'Upload failed. Please try again.');
                }
            });
        },
        
        /**
         * Add icon to preview
         */
        addIconToPreview: function(iconData) {
            const $preview = $('.icon-preview');
            
            const $item = $(`
                <div class="icon-preview-item" data-icon-id="${iconData.id}">
                    <img src="${iconData.url}" alt="${iconData.name}" />
                    <button class="remove-icon" title="Remove icon">&times;</button>
                </div>
            `);
            
            $preview.append($item);
        },
        
        /**
         * Handle icon selection
         */
        handleIconSelect: function(e) {
            e.preventDefault();
            
            const $item = $(e.currentTarget);
            const iconId = $item.data('icon-id');
            
            // Update selection
            $('.icon-preview-item').removeClass('active');
            $item.addClass('active');
            
            // Update hidden input
            $('input[name="wp_favorites_custom_icon"]').val(iconId);
        },
        
        /**
         * Handle icon removal
         */
        handleIconRemove: function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const $item = $(e.currentTarget).closest('.icon-preview-item');
            const iconId = $item.data('icon-id');
            
            if (confirm('Are you sure you want to remove this icon?')) {
                this.removeIcon(iconId, $item);
            }
        },
        
        /**
         * Remove icon
         */
        removeIcon: function(iconId, $item) {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'wp_favorites_remove_icon',
                    nonce: wpFavoritesAdmin.nonce,
                    icon_id: iconId
                },
                success: (response) => {
                    if (response.success) {
                        $item.fadeOut(300, function() {
                            $(this).remove();
                        });
                        this.showNotice('success', 'Icon removed successfully');
                    } else {
                        this.showNotice('error', response.data.message || 'Failed to remove icon');
                    }
                },
                error: () => {
                    this.showNotice('error', 'Failed to remove icon');
                }
            });
        },
        
        /**
         * Initialize translation manager
         */
        initTranslationManager: function() {
            // Add translation progress indicators
            this.updateTranslationProgress();
            
            // Add preview functionality
            $('.translation-category input').each(function() {
                const $input = $(this);
                const $preview = $('<div class="translation-preview"></div>');
                $input.after($preview);
                
                $input.on('input', function() {
                    const value = $(this).val() || $(this).attr('placeholder');
                    $preview.text(value);
                });
                
                // Initial preview
                $input.trigger('input');
            });
        },
        
        /**
         * Handle translation changes
         */
        handleTranslationChange: function(e) {
            const $input = $(e.currentTarget);
            const $category = $input.closest('.translation-category');
            
            // Mark as modified
            $category.addClass('modified');
            
            // Update progress
            this.updateTranslationProgress();
        },
        
        /**
         * Update translation progress
         */
        updateTranslationProgress: function() {
            $('.translation-category').each(function() {
                const $category = $(this);
                const $inputs = $category.find('input[type="text"]');
                const total = $inputs.length;
                const filled = $inputs.filter(function() {
                    return $(this).val().trim() !== '';
                }).length;
                
                const percentage = total > 0 ? Math.round((filled / total) * 100) : 0;
                
                let $progress = $category.find('.translation-progress');
                if (!$progress.length) {
                    $progress = $('<div class="translation-progress"></div>');
                    $category.find('h3').append($progress);
                }
                
                $progress.html(`<span class="progress-bar" style="width: ${percentage}%"></span> ${percentage}%`);
            });
        },
        
        /**
         * Save translations
         */
        saveTranslations: function(e) {
            e.preventDefault();
            
            const translations = {};
            
            $('.translation-category input[type="text"]').each(function() {
                const $input = $(this);
                const name = $input.attr('name');
                const value = $input.val().trim();
                
                if (name && value) {
                    const matches = name.match(/wp_favorites_custom_messages\[([^\]]+)\]\[([^\]]+)\]/);
                    if (matches) {
                        const category = matches[1];
                        const key = matches[2];
                        
                        if (!translations[category]) {
                            translations[category] = {};
                        }
                        translations[category][key] = value;
                    }
                }
            });
            
            this.showLoading('Saving translations...');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'wp_favorites_save_translation',
                    nonce: wpFavoritesAdmin.nonce,
                    translations: translations
                },
                success: (response) => {
                    this.hideLoading();
                    
                    if (response.success) {
                        this.showNotice('success', response.data.message);
                        $('.translation-category').removeClass('modified');
                    } else {
                        this.showNotice('error', response.data.message || 'Failed to save translations');
                    }
                },
                error: () => {
                    this.hideLoading();
                    this.showNotice('error', 'Failed to save translations');
                }
            });
        },
        
        /**
         * Load performance statistics
         */
        loadPerformanceStats: function() {
            if (!$('.performance-stats').length) return;
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'wp_favorites_get_stats',
                    nonce: wpFavoritesAdmin.nonce
                },
                success: (response) => {
                    if (response.success) {
                        this.updatePerformanceStats(response.data);
                    }
                }
            });
        },
        
        /**
         * Update performance statistics
         */
        updatePerformanceStats: function(stats) {
            const $container = $('.performance-stats');
            
            $container.html(`
                <div class="performance-stat">
                    <div class="performance-stat-value">${stats.total_favorites || 0}</div>
                    <div class="performance-stat-label">Total Favorites</div>
                </div>
                <div class="performance-stat">
                    <div class="performance-stat-value">${stats.unique_users || 0}</div>
                    <div class="performance-stat-label">Active Users</div>
                </div>
                <div class="performance-stat">
                    <div class="performance-stat-value">${stats.cache_hit_rate || 0}%</div>
                    <div class="performance-stat-label">Cache Hit Rate</div>
                </div>
                <div class="performance-stat">
                    <div class="performance-stat-value">${stats.avg_response_time || 0}ms</div>
                    <div class="performance-stat-label">Avg Response Time</div>
                </div>
            `);
        },
        
        /**
         * Clear cache
         */
        clearCache: function(e) {
            e.preventDefault();
            
            if (!confirm('Are you sure you want to clear all cache?')) {
                return;
            }
            
            this.showLoading('Clearing cache...');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'wp_favorites_clear_cache',
                    nonce: wpFavoritesAdmin.nonce
                },
                success: (response) => {
                    this.hideLoading();
                    
                    if (response.success) {
                        this.showNotice('success', 'Cache cleared successfully');
                        this.loadPerformanceStats();
                    } else {
                        this.showNotice('error', response.data.message || 'Failed to clear cache');
                    }
                },
                error: () => {
                    this.hideLoading();
                    this.showNotice('error', 'Failed to clear cache');
                }
            });
        },
        
        /**
         * Optimize database
         */
        optimizeDatabase: function(e) {
            e.preventDefault();
            
            if (!confirm('Are you sure you want to optimize the database?')) {
                return;
            }
            
            this.showLoading('Optimizing database...');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'wp_favorites_optimize_db',
                    nonce: wpFavoritesAdmin.nonce
                },
                success: (response) => {
                    this.hideLoading();
                    
                    if (response.success) {
                        this.showNotice('success', `Database optimized. ${response.data.removed} orphaned records removed.`);
                        this.loadPerformanceStats();
                    } else {
                        this.showNotice('error', response.data.message || 'Failed to optimize database');
                    }
                },
                error: () => {
                    this.hideLoading();
                    this.showNotice('error', 'Failed to optimize database');
                }
            });
        },
        
        /**
         * Validate form before submission
         */
        validateForm: function(e) {
            const $form = $(e.currentTarget);
            let isValid = true;
            
            // Validate required fields
            $form.find('[required]').each(function() {
                const $field = $(this);
                if (!$field.val().trim()) {
                    $field.addClass('error');
                    isValid = false;
                } else {
                    $field.removeClass('error');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                this.showNotice('error', 'Please fill in all required fields');
            }
            
            return isValid;
        },
        
        /**
         * Show loading indicator
         */
        showLoading: function(message = 'Loading...') {
            if (!$('.wp-favorites-loading').length) {
                $('body').append(`
                    <div class="wp-favorites-loading">
                        <div class="wp-favorites-spinner"></div>
                        <span class="loading-message">${message}</span>
                    </div>
                `);
            } else {
                $('.loading-message').text(message);
                $('.wp-favorites-loading').show();
            }
        },
        
        /**
         * Hide loading indicator
         */
        hideLoading: function() {
            $('.wp-favorites-loading').hide();
        },
        
        /**
         * Show admin notice
         */
        showNotice: function(type, message) {
            const $notice = $(`
                <div class="wp-favorites-notice ${type}">
                    <p>${message}</p>
                </div>
            `);
            
            $('.wp-favorites-admin .wrap h1').after($notice);
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                $notice.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 5000);
        }
    };
    
    // Initialize when DOM is ready
    $(document).ready(function() {
        if (typeof wpFavoritesAdmin !== 'undefined') {
            WPFavoritesAdmin.init();
        }
    });
    
    // Expose to global scope
    window.WPFavoritesAdmin = WPFavoritesAdmin;
    
})(jQuery);
