# WP Favorites Translation Template
# Copyright (C) 2024 PixelHunter
# This file is distributed under the same license as the WP Favorites plugin.
msgid ""
msgstr ""
"Project-Id-Version: WP Favorites 1.0.0\n"
"Report-Msgid-Bugs-To: https://github.com/pixelhunter1/wp-favorites/issues\n"
"POT-Creation-Date: 2024-01-01 12:00+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"X-Generator: WP-CLI 2.8.1\n"
"X-Domain: wp-favorites\n"

#: wp-favorites.php:125
#, php-format
msgid "%s requires WooCommerce to be installed and active."
msgstr ""

#: includes/class-favorites-core.php:145
msgid "Product added to favorites"
msgstr ""

#: includes/class-favorites-core.php:146
msgid "Product removed from favorites"
msgstr ""

#: includes/class-favorites-core.php:147
msgid "Favorites updated successfully"
msgstr ""

#: includes/class-favorites-core.php:150
msgid "Failed to add product to favorites"
msgstr ""

#: includes/class-favorites-core.php:151
msgid "Failed to remove product from favorites"
msgstr ""

#: includes/class-favorites-core.php:152
msgid "Product not found"
msgstr ""

#: includes/class-favorites-core.php:153
msgid "Please log in to use favorites"
msgstr ""

#: includes/class-favorites-core.php:156
msgid "Your favorites list is empty"
msgstr ""

#: includes/class-favorites-core.php:157
msgid "Loading favorites..."
msgstr ""

#: includes/class-favorites-core.php:158
msgid "No products match your criteria"
msgstr ""

#: includes/class-favorites-core.php:161
msgid "Are you sure you want to remove this product from favorites?"
msgstr ""

#: includes/class-favorites-core.php:162
msgid "Are you sure you want to clear all favorites?"
msgstr ""

#: includes/class-favorites-core.php:195
msgid "Remove from favorites"
msgstr ""

#: includes/class-favorites-core.php:196
msgid "Add to favorites"
msgstr ""

#: includes/class-favorites-core.php:260
msgid "Please log in to view your favorites."
msgstr ""

#: includes/class-favorites-admin.php:30
msgid "WP Favorites"
msgstr ""

#: includes/class-favorites-admin.php:31
msgid "Favorites"
msgstr ""

#: includes/class-favorites-admin.php:39
msgid "General Settings"
msgstr ""

#: includes/class-favorites-admin.php:40
msgid "General"
msgstr ""

#: includes/class-favorites-admin.php:46
msgid "Performance Settings"
msgstr ""

#: includes/class-favorites-admin.php:47
msgid "Performance"
msgstr ""

#: includes/class-favorites-admin.php:53
msgid "Translation Management"
msgstr ""

#: includes/class-favorites-admin.php:54
msgid "Translations"
msgstr ""

#: includes/class-favorites-admin.php:85
msgid "Enable Caching"
msgstr ""

#: includes/class-favorites-admin.php:89
msgid "Enable object caching for better performance"
msgstr ""

#: includes/class-favorites-admin.php:95
msgid "Cache Duration"
msgstr ""

#: includes/class-favorites-admin.php:102
msgid "Cache duration in seconds (300-86400)"
msgstr ""

#: includes/class-favorites-admin.php:107
msgid "User Notifications"
msgstr ""

#: includes/class-favorites-admin.php:111
msgid "Enable toast notifications for user actions"
msgstr ""

#: includes/class-favorites-admin.php:117
msgid "Notification Duration"
msgstr ""

#: includes/class-favorites-admin.php:124
msgid "Notification display duration in milliseconds"
msgstr ""

#: includes/class-favorites-admin.php:137
msgid "Customize frontend user messages and notifications. These will override the default translations."
msgstr ""

#: includes/class-favorites-admin.php:147
msgid "Messages"
msgstr ""

#: includes/class-favorites-admin.php:157
#, php-format
msgid "Key: %s"
msgstr ""

#: includes/class-favorites-admin.php:172
msgid "Save Translations"
msgstr ""

#: includes/class-favorites-admin.php:185
msgid "Favorites Page"
msgstr ""

#: includes/class-favorites-admin.php:191
msgid "Select a page"
msgstr ""

#: includes/class-favorites-admin.php:196
msgid "Select the page where favorites will be displayed"
msgstr ""

#: includes/class-favorites-admin.php:202
msgid "Icon Position"
msgstr ""

#: includes/class-favorites-admin.php:205
msgid "Top Right"
msgstr ""

#: includes/class-favorites-admin.php:208
msgid "Top Left"
msgstr ""

#: includes/class-favorites-admin.php:211
msgid "Bottom Right"
msgstr ""

#: includes/class-favorites-admin.php:214
msgid "Bottom Left"
msgstr ""

#: includes/class-favorites-admin.php:220
msgid "Icon Size"
msgstr ""

#: includes/class-favorites-admin.php:223
msgid "Small (16px)"
msgstr ""

#: includes/class-favorites-admin.php:226
msgid "Medium (24px)"
msgstr ""

#: includes/class-favorites-admin.php:229
msgid "Large (32px)"
msgstr ""

#: includes/class-favorites-admin.php:275
msgid "Insufficient permissions"
msgstr ""

#: includes/class-favorites-admin.php:279
msgid "Icon uploaded successfully"
msgstr ""

#: includes/class-favorites-admin.php:290
msgid "Translation saved successfully"
msgstr ""

#: includes/class-favorites-ajax.php:45
msgid "Security check failed"
msgstr ""

#: includes/class-favorites-ajax.php:52
msgid "Invalid product ID"
msgstr ""

#: includes/class-favorites-ajax.php:225
msgid "All favorites cleared successfully"
msgstr ""

#: includes/class-favorites-ajax.php:230
msgid "Failed to clear favorites"
msgstr ""

#: assets/js/frontend-script.js:1
msgid "Close notification"
msgstr ""

#: assets/js/frontend-script.js:1
msgid "Request timed out. Please try again."
msgstr ""

#: assets/js/frontend-script.js:1
msgid "Network error. Please check your connection."
msgstr ""

#: assets/js/frontend-script.js:1
msgid "Failed to clear favorites"
msgstr ""

#: assets/js/frontend-script.js:1
msgid "Start adding products to your favorites to see them here."
msgstr ""

#: assets/js/frontend-script.js:1
msgid "Browse Products"
msgstr ""

#. Plugin Name of the plugin
msgid "WP Favorites"
msgstr ""

#. Plugin URI of the plugin
msgid "https://github.com/pixelhunter1/wp-favorites"
msgstr ""

#. Description of the plugin
msgid "High-performance favorites system for WooCommerce with Breakdance integration and multilingual support."
msgstr ""

#. Author of the plugin
msgid "PixelHunter"
msgstr ""

#. Author URI of the plugin
msgid "https://pixelhunter.dev"
msgstr ""
