<?php
/**
 * Test script for WP Favorites contexts
 * Add this to your theme's functions.php temporarily to test favorite button display
 */

// Only run on frontend
if (!is_admin()) {
    add_action('wp_footer', 'wp_favorites_test_contexts');
}

function wp_favorites_test_contexts() {
    if (!function_exists('WC') || !class_exists('WP_Favorites_Core')) {
        return;
    }
    
    ?>
    <div id="wp-favorites-test-panel" style="position: fixed; bottom: 20px; right: 20px; background: #fff; padding: 15px; border: 2px solid #0073aa; border-radius: 5px; z-index: 99999; max-width: 350px; font-family: Arial, sans-serif; font-size: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-height: 400px; overflow-y: auto;">
        <h3 style="margin: 0 0 10px 0; color: #0073aa; font-size: 14px;">WP Favorites Test Panel</h3>
        
        <div id="context-info">
            <p><strong>Current Page:</strong> <span id="page-type">Loading...</span></p>
            <p><strong>WooCommerce Active:</strong> <?php echo function_exists('WC') ? '✓ Yes' : '✗ No'; ?></p>
            <p><strong>Favorite Buttons Found:</strong> <span id="button-count">Counting...</span></p>
            <p><strong>Contexts Detected:</strong> <span id="contexts-found">Analyzing...</span></p>
        </div>
        
        <div id="test-results" style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #ddd;">
            <h4 style="margin: 0 0 5px 0; font-size: 12px;">Test Results:</h4>
            <div id="results-list"></div>
        </div>
        
        <button onclick="runFavoritesTest()" style="margin-top: 10px; padding: 5px 10px; background: #0073aa; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 11px;">Run Test</button>
        <button onclick="toggleTestPanel()" style="margin-top: 10px; margin-left: 5px; padding: 5px 10px; background: #666; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 11px;">Hide</button>
    </div>

    <script>
    function runFavoritesTest() {
        // Detect current page type
        let pageType = 'Unknown';
        if (document.body.classList.contains('woocommerce-shop')) pageType = 'Shop Page';
        else if (document.body.classList.contains('single-product')) pageType = 'Single Product';
        else if (document.body.classList.contains('product-template-default')) pageType = 'Single Product';
        else if (document.body.classList.contains('tax-product_cat')) pageType = 'Category Page';
        else if (document.body.classList.contains('woocommerce-cart')) pageType = 'Cart Page';
        else if (document.body.classList.contains('search-results')) pageType = 'Search Results';
        else if (document.body.classList.contains('home')) pageType = 'Homepage';
        
        document.getElementById('page-type').textContent = pageType;
        
        // Count favorite buttons
        const buttons = document.querySelectorAll('.wp-favorites-button');
        document.getElementById('button-count').textContent = buttons.length;
        
        // Analyze contexts
        const contexts = new Set();
        buttons.forEach(button => {
            const classes = button.className.split(' ');
            classes.forEach(cls => {
                if (cls.startsWith('context-')) {
                    contexts.add(cls.replace('context-', ''));
                }
            });
        });
        
        document.getElementById('contexts-found').textContent = Array.from(contexts).join(', ') || 'None';
        
        // Test specific areas
        const testResults = [];

        // Test shop/category loops
        const productLoops = document.querySelectorAll('.products .product, ul.products li.product');
        const loopButtonCount = Array.from(productLoops).reduce((count, product) => {
            return count + product.querySelectorAll('.wp-favorites-button').length;
        }, 0);
        testResults.push(`Product Loops: ${loopButtonCount}/${productLoops.length} products have buttons`);

        // Check for duplicates in product loops
        let duplicateCount = 0;
        productLoops.forEach(product => {
            const productButtons = product.querySelectorAll('.wp-favorites-button');
            if (productButtons.length > 1) {
                duplicateCount++;
            }
        });
        if (duplicateCount > 0) {
            testResults.push(`⚠ Duplicates: ${duplicateCount} products have multiple buttons`);
        }

        // Test related products
        const relatedSection = document.querySelector('.related.products, .up-sells, .cross-sells');
        if (relatedSection) {
            const relatedProducts = relatedSection.querySelectorAll('.product');
            const relatedButtons = relatedSection.querySelectorAll('.wp-favorites-button');
            testResults.push(`Related/Up-sells/Cross-sells: ${relatedButtons.length}/${relatedProducts.length} products have buttons`);
        }

        // Test widgets
        const widgets = document.querySelectorAll('.widget_products, .widget_recent_products, .widget_featured_products');
        let widgetButtonCount = 0;
        let widgetProductCount = 0;
        widgets.forEach(widget => {
            const products = widget.querySelectorAll('.product_list_widget li, .product-item');
            const buttons = widget.querySelectorAll('.wp-favorites-button');
            widgetButtonCount += buttons.length;
            widgetProductCount += products.length;
        });
        if (widgets.length > 0) {
            testResults.push(`Widgets: ${widgetButtonCount}/${widgetProductCount} products have buttons`);
        }

        // Test single product page
        if (document.querySelector('.single-product')) {
            const singleProductButtons = document.querySelectorAll('.single-product .wp-favorites-button');
            testResults.push(`Single Product: ${singleProductButtons.length} button(s) found`);
        }

        // Test positioning consistency
        const allButtons = document.querySelectorAll('.wp-favorites-button');
        const positionClasses = new Set();
        allButtons.forEach(button => {
            const classes = button.className.split(' ');
            classes.forEach(cls => {
                if (cls.startsWith('position-')) {
                    positionClasses.add(cls);
                }
            });
        });
        testResults.push(`Position Classes: ${Array.from(positionClasses).join(', ') || 'None'}`);

        // Test wrapper consistency
        const wrappersWithoutButtons = document.querySelectorAll('.wp-favorites-wrapper:not(:has(.wp-favorites-button))');
        if (wrappersWithoutButtons.length > 0) {
            testResults.push(`⚠ Empty Wrappers: ${wrappersWithoutButtons.length} wrappers without buttons`);
        }
        
        // Display results
        const resultsList = document.getElementById('results-list');
        resultsList.innerHTML = testResults.map(result => `<div style="margin: 2px 0; font-size: 11px;">• ${result}</div>`).join('');
        
        // Check for common issues
        const issues = [];
        if (buttons.length === 0) {
            issues.push('No favorite buttons found on page');
        }
        
        const hiddenButtons = Array.from(buttons).filter(btn => {
            const style = window.getComputedStyle(btn.closest('.wp-favorites-wrapper') || btn);
            return style.display === 'none' || style.visibility === 'hidden';
        });
        
        if (hiddenButtons.length > 0) {
            issues.push(`${hiddenButtons.length} buttons are hidden by CSS`);
        }
        
        if (issues.length > 0) {
            resultsList.innerHTML += '<div style="margin-top: 5px; padding-top: 5px; border-top: 1px solid #ddd; color: #d63638;"><strong>Issues:</strong></div>';
            resultsList.innerHTML += issues.map(issue => `<div style="margin: 2px 0; font-size: 11px; color: #d63638;">⚠ ${issue}</div>`).join('');
        }
    }
    
    function toggleTestPanel() {
        const panel = document.getElementById('wp-favorites-test-panel');
        panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    }
    
    // Auto-run test after page load
    setTimeout(runFavoritesTest, 1000);
    
    // Re-run test when AJAX content loads
    if (typeof jQuery !== 'undefined') {
        jQuery(document).on('updated_wc_div updated_cart_totals', function() {
            setTimeout(runFavoritesTest, 500);
        });
    }
    </script>
    <?php
}
