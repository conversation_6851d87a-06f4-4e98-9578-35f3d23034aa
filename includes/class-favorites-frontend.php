<?php
/**
 * WP Favorites Frontend Class
 * 
 * Handles frontend display and user interactions
 */

if (!defined('ABSPATH')) {
    exit;
}

class WP_Favorites_Frontend {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Add favorites data to localized script
        add_filter('wp_favorites_localize_data', array($this, 'add_frontend_data'));
        
        // Add body classes
        add_filter('body_class', array($this, 'add_body_classes'));
        
        // Handle favorites page content
        add_filter('the_content', array($this, 'maybe_add_favorites_content'));
        
        // Add structured data
        add_action('wp_head', array($this, 'add_structured_data'));
    }
    
    /**
     * Add frontend data to localized script
     */
    public function add_frontend_data($data) {
        // Add user favorites if logged in
        if (is_user_logged_in()) {
            $user_id = get_current_user_id();
            $data['userFavorites'] = $this->get_user_favorites($user_id);
            $data['isLoggedIn'] = true;
        } else {
            $data['userFavorites'] = array();
            $data['isLoggedIn'] = false;
        }
        
        // Add shop URL
        if (function_exists('wc_get_page_id')) {
            $shop_id = wc_get_page_id('shop');
            $data['shopUrl'] = $shop_id ? get_permalink($shop_id) : home_url();
        }
        
        // Add favorites page URL
        $favorites_page_id = get_option('wp_favorites_page_id', 0);
        if ($favorites_page_id) {
            $data['favoritesUrl'] = get_permalink($favorites_page_id);
        }
        
        return $data;
    }
    
    /**
     * Add body classes
     */
    public function add_body_classes($classes) {
        // Add class if on favorites page
        $favorites_page_id = get_option('wp_favorites_page_id', 0);
        if ($favorites_page_id && is_page($favorites_page_id)) {
            $classes[] = 'wp-favorites-page';
        }
        
        // Add class if user has favorites
        if (is_user_logged_in()) {
            $user_id = get_current_user_id();
            $favorites_count = $this->get_user_favorites_count($user_id);
            if ($favorites_count > 0) {
                $classes[] = 'has-favorites';
                $classes[] = 'favorites-count-' . $favorites_count;
            } else {
                $classes[] = 'no-favorites';
            }
        }
        
        return $classes;
    }
    
    /**
     * Maybe add favorites content to page
     */
    public function maybe_add_favorites_content($content) {
        $favorites_page_id = get_option('wp_favorites_page_id', 0);
        
        if ($favorites_page_id && is_page($favorites_page_id) && is_main_query()) {
            // Add favorites list to the content
            $favorites_content = $this->render_favorites_page();
            $content .= $favorites_content;
        }
        
        return $content;
    }
    
    /**
     * Render favorites page
     */
    public function render_favorites_page() {
        if (!is_user_logged_in()) {
            return $this->render_login_message();
        }
        
        $user_id = get_current_user_id();
        $favorites = $this->get_user_favorites_with_products($user_id);
        
        ob_start();
        ?>
        <div class="wp-favorites-container">
            <?php if (empty($favorites)): ?>
                <?php echo $this->render_empty_state(); ?>
            <?php else: ?>
                <?php echo $this->render_favorites_grid($favorites); ?>
            <?php endif; ?>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render favorites list (for shortcode)
     */
    public function render_favorites_list($atts = array()) {
        $atts = wp_parse_args($atts, array(
            'columns' => 4,
            'limit' => -1,
            'show_remove' => 'yes'
        ));
        
        if (!is_user_logged_in()) {
            echo $this->render_login_message();
            return;
        }
        
        $user_id = get_current_user_id();
        $favorites = $this->get_user_favorites_with_products($user_id, $atts['limit']);
        
        if (empty($favorites)) {
            echo $this->render_empty_state();
            return;
        }
        
        echo $this->render_favorites_grid($favorites, $atts);
    }
    
    /**
     * Render login message
     */
    private function render_login_message() {
        $login_url = wp_login_url(get_permalink());
        
        ob_start();
        ?>
        <div class="wp-favorites-login-message">
            <div class="wp-favorites-empty">
                <div class="wp-favorites-empty-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                    </svg>
                </div>
                <h3 class="wp-favorites-empty-title">
                    <?php _e('Login Required', 'wp-favorites'); ?>
                </h3>
                <p class="wp-favorites-empty-message">
                    <?php _e('Please log in to view your favorites.', 'wp-favorites'); ?>
                </p>
                <a href="<?php echo esc_url($login_url); ?>" class="wp-favorites-empty-action">
                    <?php _e('Login', 'wp-favorites'); ?>
                </a>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render empty state
     */
    private function render_empty_state() {
        $shop_url = function_exists('wc_get_page_id') ? get_permalink(wc_get_page_id('shop')) : home_url();
        
        ob_start();
        ?>
        <div class="wp-favorites-empty">
            <div class="wp-favorites-empty-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                </svg>
            </div>
            <h3 class="wp-favorites-empty-title">
                <?php _e('Your favorites list is empty', 'wp-favorites'); ?>
            </h3>
            <p class="wp-favorites-empty-message">
                <?php _e('Start adding products to your favorites to see them here.', 'wp-favorites'); ?>
            </p>
            <a href="<?php echo esc_url($shop_url); ?>" class="wp-favorites-empty-action">
                <?php _e('Browse Products', 'wp-favorites'); ?>
            </a>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render favorites grid
     */
    private function render_favorites_grid($favorites, $atts = array()) {
        $atts = wp_parse_args($atts, array(
            'columns' => 4,
            'show_remove' => 'yes'
        ));
        
        $columns = intval($atts['columns']);
        $show_remove = $atts['show_remove'] === 'yes';
        
        ob_start();
        ?>
        <div class="wp-favorites-list columns-<?php echo esc_attr($columns); ?>">
            <?php foreach ($favorites as $product): ?>
                <div class="wp-favorites-item" data-product-id="<?php echo esc_attr($product['id']); ?>">
                    <div class="wp-favorites-item-image">
                        <a href="<?php echo esc_url($product['url']); ?>">
                            <?php if ($product['image']): ?>
                                <img src="<?php echo esc_url($product['image']); ?>" 
                                     alt="<?php echo esc_attr($product['name']); ?>"
                                     loading="lazy" />
                            <?php else: ?>
                                <div class="wp-favorites-no-image">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                        <circle cx="8.5" cy="8.5" r="1.5"></circle>
                                        <polyline points="21,15 16,10 5,21"></polyline>
                                    </svg>
                                </div>
                            <?php endif; ?>
                        </a>
                    </div>
                    
                    <div class="wp-favorites-item-content">
                        <h3 class="wp-favorites-item-title">
                            <a href="<?php echo esc_url($product['url']); ?>">
                                <?php echo esc_html($product['name']); ?>
                            </a>
                        </h3>
                        
                        <div class="wp-favorites-item-price">
                            <?php echo $product['price']; ?>
                        </div>
                        
                        <div class="wp-favorites-item-actions">
                            <a href="<?php echo esc_url($product['url']); ?>" 
                               class="wp-favorites-view-btn">
                                <?php _e('View Product', 'wp-favorites'); ?>
                            </a>
                            
                            <?php if ($show_remove): ?>
                                <button class="wp-favorites-remove-btn" 
                                        data-product-id="<?php echo esc_attr($product['id']); ?>">
                                    <?php _e('Remove', 'wp-favorites'); ?>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <?php if ($show_remove && count($favorites) > 1): ?>
            <div class="wp-favorites-actions">
                <button class="wp-favorites-clear-all button">
                    <?php _e('Clear All Favorites', 'wp-favorites'); ?>
                </button>
            </div>
        <?php endif; ?>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get user favorites (product IDs only)
     */
    private function get_user_favorites($user_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'wp_favorites';
        
        $favorites = $wpdb->get_col($wpdb->prepare(
            "SELECT product_id FROM $table_name WHERE user_id = %d ORDER BY date_added DESC",
            $user_id
        ));
        
        return array_map('strval', $favorites);
    }
    
    /**
     * Get user favorites with product data
     */
    private function get_user_favorites_with_products($user_id, $limit = -1) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'wp_favorites';
        
        $limit_sql = $limit > 0 ? $wpdb->prepare("LIMIT %d", $limit) : '';
        
        $favorites = $wpdb->get_results($wpdb->prepare(
            "SELECT product_id, date_added FROM $table_name 
             WHERE user_id = %d 
             ORDER BY date_added DESC 
             $limit_sql",
            $user_id
        ));
        
        $products = array();
        foreach ($favorites as $favorite) {
            $product = wc_get_product($favorite->product_id);
            if ($product && $product->get_status() === 'publish') {
                $products[] = array(
                    'id' => $product->get_id(),
                    'name' => $product->get_name(),
                    'price' => $product->get_price_html(),
                    'image' => wp_get_attachment_image_url($product->get_image_id(), 'medium'),
                    'url' => $product->get_permalink(),
                    'date_added' => $favorite->date_added
                );
            }
        }
        
        return $products;
    }
    
    /**
     * Get user favorites count
     */
    private function get_user_favorites_count($user_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'wp_favorites';
        
        return intval($wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE user_id = %d",
            $user_id
        )));
    }
    
    /**
     * Add structured data for SEO
     */
    public function add_structured_data() {
        $favorites_page_id = get_option('wp_favorites_page_id', 0);
        
        if ($favorites_page_id && is_page($favorites_page_id) && is_user_logged_in()) {
            $user_id = get_current_user_id();
            $favorites_count = $this->get_user_favorites_count($user_id);
            
            $structured_data = array(
                '@context' => 'https://schema.org',
                '@type' => 'CollectionPage',
                'name' => get_the_title($favorites_page_id),
                'description' => sprintf(
                    _n('%d favorite product', '%d favorite products', $favorites_count, 'wp-favorites'),
                    $favorites_count
                ),
                'url' => get_permalink($favorites_page_id),
                'numberOfItems' => $favorites_count
            );
            
            echo '<script type="application/ld+json">' . wp_json_encode($structured_data) . '</script>' . "\n";
        }
    }
}
