<?php
/**
 * Debug script para verificar se o WP Favorites está funcionando corretamente
 * 
 * Adicione ?debug_favorites=1 na URL para ver informações de debug
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Only run if debug parameter is present
if (!isset($_GET['debug_favorites'])) {
    return;
}

// Only for administrators
if (!current_user_can('manage_options')) {
    return;
}

add_action('wp_footer', function() {
    if (!is_admin()) {
        echo '<div style="position: fixed; top: 20px; left: 20px; background: #fff; padding: 15px; border: 2px solid #0073aa; border-radius: 5px; z-index: 99999; max-width: 400px; font-family: Arial, sans-serif; font-size: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">';
        echo '<h3 style="margin: 0 0 10px 0; color: #0073aa;">WP Favorites Debug</h3>';
        
        // Check current page type
        echo '<p><strong>Página atual:</strong> ';
        if (is_home()) echo 'Homepage';
        elseif (is_front_page()) echo 'Front Page';
        elseif (is_shop()) echo 'Shop Page';
        elseif (is_product()) echo 'Single Product';
        elseif (is_product_category()) echo 'Product Category';
        elseif (is_cart()) echo 'Cart Page';
        else echo 'Other';
        echo '</p>';
        
        // Check if WooCommerce is active
        echo '<p><strong>WooCommerce:</strong> ' . (function_exists('WC') ? '✓ Ativo' : '✗ Inativo') . '</p>';
        
        // Check if assets should load
        $core = WP_Favorites_Core::get_instance();
        $should_load = false;
        if ($core) {
            $reflection = new ReflectionClass($core);
            $method = $reflection->getMethod('should_load_frontend_assets');
            $method->setAccessible(true);
            $should_load = $method->invoke($core);
        }
        echo '<p><strong>Assets carregados:</strong> ' . ($should_load ? '✓ Sim' : '✗ Não') . '</p>';
        
        // Check for favorite buttons
        echo '<p><strong>Botões encontrados:</strong> <span id="favorites-count">Contando...</span></p>';
        
        // Check settings
        $display_locations = get_option('wp_favorites_display_locations', array());
        echo '<p><strong>Locais configurados:</strong> ' . count($display_locations) . '</p>';
        
        echo '<script>
        setTimeout(function() {
            var count = document.querySelectorAll(".wp-favorites-button").length;
            document.getElementById("favorites-count").textContent = count + " encontrados";
        }, 1000);
        </script>';
        
        echo '</div>';
    }
});
